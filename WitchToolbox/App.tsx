import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';

// 导入导航器
import StackNavigator from './src/navigation/StackNavigator';

// 导入样式
import Colors from './src/styles/colors';

const App: React.FC = () => {
  return (
    <NavigationContainer>
      <StatusBar
        style="light"
        backgroundColor={Colors.background.primary}
        translucent={Platform.OS === 'android'}
      />
      <StackNavigator />
    </NavigationContainer>
  );
};

export default App;
