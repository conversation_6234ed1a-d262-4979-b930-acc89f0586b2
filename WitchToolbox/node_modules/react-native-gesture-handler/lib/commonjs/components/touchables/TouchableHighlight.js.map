{"version": 3, "sources": ["TouchableHighlight.tsx"], "names": ["TouchableHighlight", "Component", "constructor", "props", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setState", "extraChildStyle", "opacity", "activeOpacity", "extraUnderlayStyle", "backgroundColor", "underlayColor", "onShowUnderlay", "onPress", "onPressIn", "onPressOut", "onLongPress", "onHideUnderlay", "_from", "to", "TOUCHABLE_STATE", "BEGAN", "showUnderlay", "UNDETERMINED", "MOVED_OUTSIDE", "<PERSON><PERSON><PERSON><PERSON>", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "child", "React", "Children", "only", "cloneElement", "style", "StyleSheet", "compose", "render", "rest", "onStateChange", "GenericTouchable", "defaultProps", "delayPressOut"], "mappings": ";;;;;;;AAAA;;AAEA;;AAEA;;;;;;;;;;AAuBA;AACA;AACA;AACA;AACA;AACe,MAAMA,kBAAN,SAAiCC,eAAjC,CAGb;AAQAC,EAAAA,WAAW,CAACC,KAAD,EAAiC;AAC1C,UAAMA,KAAN;;AAD0C,0CAS7B,MAAM;AAAA;;AACnB,UAAI,CAAC,KAAKC,eAAL,EAAL,EAA6B;AAC3B;AACD;;AACD,WAAKC,QAAL,CAAc;AACZC,QAAAA,eAAe,EAAE;AACfC,UAAAA,OAAO,EAAE,KAAKJ,KAAL,CAAWK;AADL,SADL;AAIZC,QAAAA,kBAAkB,EAAE;AAClBC,UAAAA,eAAe,EAAE,KAAKP,KAAL,CAAWQ;AADV;AAJR,OAAd;AAQA,mDAAKR,KAAL,EAAWS,cAAX;AACD,KAtB2C;;AAAA,6CAwB1B,MAChB,KAAKT,KAAL,CAAWU,OAAX,IACA,KAAKV,KAAL,CAAWW,SADX,IAEA,KAAKX,KAAL,CAAWY,UAFX,IAGA,KAAKZ,KAAL,CAAWa,WA5B+B;;AAAA,0CA8B7B,MAAM;AAAA;;AACnB,WAAKX,QAAL,CAAc;AACZC,QAAAA,eAAe,EAAE,IADL;AAEZG,QAAAA,kBAAkB,EAAE;AAFR,OAAd;AAIA,oDAAKN,KAAL,EAAWc,cAAX;AACD,KApC2C;;AAAA,2CAmD5B,CAACC,KAAD,EAAgBC,EAAhB,KAA+B;AAC7C,UAAIA,EAAE,KAAKC,kCAAgBC,KAA3B,EAAkC;AAChC,aAAKC,YAAL;AACD,OAFD,MAEO,IACLH,EAAE,KAAKC,kCAAgBG,YAAvB,IACAJ,EAAE,KAAKC,kCAAgBI,aAFlB,EAGL;AACA,aAAKC,YAAL;AACD;AACF,KA5D2C;;AAE1C,SAAKC,KAAL,GAAa;AACXpB,MAAAA,eAAe,EAAE,IADN;AAEXG,MAAAA,kBAAkB,EAAE;AAFT,KAAb;AAID,GAdD,CAgBA;;;AA8BAkB,EAAAA,cAAc,GAAG;AACf,QAAI,CAAC,KAAKxB,KAAL,CAAWyB,QAAhB,EAA0B;AACxB,0BAAO,oBAAC,iBAAD,OAAP;AACD;;AAED,UAAMC,KAAK,GAAGC,KAAK,CAACC,QAAN,CAAeC,IAAf,CACZ,KAAK7B,KAAL,CAAWyB,QADC,CAAd,CALe,CAOqB;;AACpC,wBAAOE,KAAK,CAACG,YAAN,CAAmBJ,KAAnB,EAA0B;AAC/BK,MAAAA,KAAK,EAAEC,wBAAWC,OAAX,CAAmBP,KAAK,CAAC1B,KAAN,CAAY+B,KAA/B,EAAsC,KAAKR,KAAL,CAAWpB,eAAjD;AADwB,KAA1B,CAAP;AAGD;;AAaD+B,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEH,MAAAA,KAAK,GAAG,EAAV;AAAc,SAAGI;AAAjB,QAA0B,KAAKnC,KAArC;AACA,UAAM;AAAEM,MAAAA;AAAF,QAAyB,KAAKiB,KAApC;AACA,wBACE,oBAAC,yBAAD,eACMY,IADN;AAEE,MAAA,KAAK,EAAE,CAACJ,KAAD,EAAQzB,kBAAR,CAFT;AAGE,MAAA,aAAa,EAAE,KAAK8B;AAHtB,QAIG,KAAKZ,cAAL,EAJH,CADF;AAQD;;AAjFD;;;;gBAHmB3B,kB,kBAIG,EACpB,GAAGwC,0BAAiBC,YADA;AAEpBjC,EAAAA,aAAa,EAAE,IAFK;AAGpBkC,EAAAA,aAAa,EAAE,GAHK;AAIpB/B,EAAAA,aAAa,EAAE;AAJK,C", "sourcesContent": ["import * as React from 'react';\nimport { Component } from 'react';\nimport GenericTouchable, { TOUCHABLE_STATE } from './GenericTouchable';\nimport type { GenericTouchableProps } from './GenericTouchableProps';\nimport {\n  StyleSheet,\n  View,\n  TouchableHighlightProps as RNTouchableHighlightProps,\n  ColorValue,\n  ViewProps,\n} from 'react-native';\n\ninterface State {\n  extraChildStyle: null | {\n    opacity?: number;\n  };\n  extraUnderlayStyle: null | {\n    backgroundColor?: ColorValue;\n  };\n}\n\n/**\n * @deprecated TouchableHighlight will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nexport type TouchableHighlightProps = RNTouchableHighlightProps &\n  GenericTouchableProps;\n\n/**\n * @deprecated TouchableHighlight will be removed in the future version of Gesture Handler. Use Pressable instead.\n *\n * TouchableHighlight follows RN's implementation\n */\nexport default class TouchableHighlight extends Component<\n  TouchableHighlightProps,\n  State\n> {\n  static defaultProps = {\n    ...GenericTouchable.defaultProps,\n    activeOpacity: 0.85,\n    delayPressOut: 100,\n    underlayColor: 'black',\n  };\n\n  constructor(props: TouchableHighlightProps) {\n    super(props);\n    this.state = {\n      extraChildStyle: null,\n      extraUnderlayStyle: null,\n    };\n  }\n\n  // Copied from RN\n  showUnderlay = () => {\n    if (!this.hasPressHandler()) {\n      return;\n    }\n    this.setState({\n      extraChildStyle: {\n        opacity: this.props.activeOpacity,\n      },\n      extraUnderlayStyle: {\n        backgroundColor: this.props.underlayColor,\n      },\n    });\n    this.props.onShowUnderlay?.();\n  };\n\n  hasPressHandler = () =>\n    this.props.onPress ||\n    this.props.onPressIn ||\n    this.props.onPressOut ||\n    this.props.onLongPress;\n\n  hideUnderlay = () => {\n    this.setState({\n      extraChildStyle: null,\n      extraUnderlayStyle: null,\n    });\n    this.props.onHideUnderlay?.();\n  };\n\n  renderChildren() {\n    if (!this.props.children) {\n      return <View />;\n    }\n\n    const child = React.Children.only(\n      this.props.children\n    ) as React.ReactElement<ViewProps>; // TODO: not sure if OK but fixes error\n    return React.cloneElement(child, {\n      style: StyleSheet.compose(child.props.style, this.state.extraChildStyle),\n    });\n  }\n\n  onStateChange = (_from: number, to: number) => {\n    if (to === TOUCHABLE_STATE.BEGAN) {\n      this.showUnderlay();\n    } else if (\n      to === TOUCHABLE_STATE.UNDETERMINED ||\n      to === TOUCHABLE_STATE.MOVED_OUTSIDE\n    ) {\n      this.hideUnderlay();\n    }\n  };\n\n  render() {\n    const { style = {}, ...rest } = this.props;\n    const { extraUnderlayStyle } = this.state;\n    return (\n      <GenericTouchable\n        {...rest}\n        style={[style, extraUnderlayStyle]}\n        onStateChange={this.onStateChange}>\n        {this.renderChildren()}\n      </GenericTouchable>\n    );\n  }\n}\n"]}