{"version": 3, "names": ["Sensor", "SensorContainer", "nativeSensors", "Map", "getSensorId", "sensorType", "config", "iosReferenceFrame", "Number", "adjustToInterfaceOrientation", "initializeSensor", "sensorId", "has", "newSensor", "set", "sensor", "get", "getSharedValue", "registerSensor", "handler", "isAvailable", "isRunning", "register", "listenersNumber", "unregisterSensor", "unregister"], "sourceRoot": "../../src", "sources": ["SensorContainer.ts"], "mappings": "AAAA,YAAY;;AASZ,OAAOA,MAAM,MAAM,aAAU;AAE7B,OAAO,MAAMC,eAAe,CAAC;EACnBC,aAAa,GAAwB,IAAIC,GAAG,CAAC,CAAC;EAEtDC,WAAWA,CAACC,UAAsB,EAAEC,MAAoB,EAAE;IACxD,OACED,UAAU,GAAG,GAAG,GAChBC,MAAM,CAACC,iBAAiB,GAAG,EAAE,GAC7BC,MAAM,CAACF,MAAM,CAACG,4BAA4B,CAAC;EAE/C;EAEAC,gBAAgBA,CACdL,UAAsB,EACtBC,MAAoB,EACkB;IACtC,MAAMK,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;IAErD,IAAI,CAAC,IAAI,CAACJ,aAAa,CAACU,GAAG,CAACD,QAAQ,CAAC,EAAE;MACrC,MAAME,SAAS,GAAG,IAAIb,MAAM,CAACK,UAAU,EAAEC,MAAM,CAAC;MAChD,IAAI,CAACJ,aAAa,CAACY,GAAG,CAACH,QAAQ,EAAEE,SAAS,CAAC;IAC7C;IAEA,MAAME,MAAM,GAAG,IAAI,CAACb,aAAa,CAACc,GAAG,CAACL,QAAQ,CAAC;IAC/C,OAAOI,MAAM,CAAEE,cAAc,CAAC,CAAC;EACjC;EAEAC,cAAcA,CACZb,UAAsB,EACtBC,MAAoB,EACpBa,OAA8D,EACtD;IACR,MAAMR,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACC,UAAU,EAAEC,MAAM,CAAC;IAErD,IAAI,CAAC,IAAI,CAACJ,aAAa,CAACU,GAAG,CAACD,QAAQ,CAAC,EAAE;MACrC,OAAO,CAAC,CAAC;IACX;IAEA,MAAMI,MAAM,GAAG,IAAI,CAACb,aAAa,CAACc,GAAG,CAACL,QAAQ,CAAC;IAC/C,IACEI,MAAM,IACNA,MAAM,CAACK,WAAW,CAAC,CAAC,KACnBL,MAAM,CAACM,SAAS,CAAC,CAAC,IAAIN,MAAM,CAACO,QAAQ,CAACH,OAAO,CAAC,CAAC,EAChD;MACAJ,MAAM,CAACQ,eAAe,EAAE;MACxB,OAAOZ,QAAQ;IACjB;IACA,OAAO,CAAC,CAAC;EACX;EAEAa,gBAAgBA,CAACb,QAAgB,EAAE;IACjC,IAAI,IAAI,CAACT,aAAa,CAACU,GAAG,CAACD,QAAQ,CAAC,EAAE;MACpC,MAAMI,MAAM,GAAG,IAAI,CAACb,aAAa,CAACc,GAAG,CAACL,QAAQ,CAAC;MAC/C,IAAII,MAAM,IAAIA,MAAM,CAACM,SAAS,CAAC,CAAC,EAAE;QAChCN,MAAM,CAACQ,eAAe,EAAE;QACxB,IAAIR,MAAM,CAACQ,eAAe,KAAK,CAAC,EAAE;UAChCR,MAAM,CAACU,UAAU,CAAC,CAAC;QACrB;MACF;IACF;EACF;AACF", "ignoreList": []}