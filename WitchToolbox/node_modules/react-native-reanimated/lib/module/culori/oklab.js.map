{"version": 3, "names": ["lrgb", "convertLrgbToOklab", "r", "g", "b", "alpha", "L", "Math", "cbrt", "M", "S", "l", "a", "convertRgbToOklab", "rgb", "lrgbColor", "convert", "fromRgb", "result", "convertOklabToLrgb", "pow", "convertOklabToRgb", "labColor", "roundChannel", "channel", "ceil", "rgbColor", "toRgb"], "sourceRoot": "../../../src", "sources": ["culori/oklab.ts"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,IAAI,MAAM,WAAQ;AAEzB,SAASC,kBAAkBA,CAAC;EAC1BC,CAAC,GAAG,CAAC;EACLC,CAAC,GAAG,CAAC;EACLC,CAAC,GAAG,CAAC;EACLC;AACQ,CAAC,EAAY;EACrB,SAAS;;EACT,MAAMC,CAAC,GAAGC,IAAI,CAACC,IAAI,CACjB,mBAAmB,GAAGN,CAAC,GAAG,YAAY,GAAGC,CAAC,GAAG,YAAY,GAAGC,CAC9D,CAAC;EACD,MAAMK,CAAC,GAAGF,IAAI,CAACC,IAAI,CACjB,kBAAkB,GAAGN,CAAC,GAAG,kBAAkB,GAAGC,CAAC,GAAG,YAAY,GAAGC,CACnE,CAAC;EACD,MAAMM,CAAC,GAAGH,IAAI,CAACC,IAAI,CACjB,mBAAmB,GAAGN,CAAC,GAAG,YAAY,GAAGC,CAAC,GAAG,kBAAkB,GAAGC,CACpE,CAAC;EAED,OAAO;IACLO,CAAC,EAAE,YAAY,GAAGL,CAAC,GAAG,WAAW,GAAGG,CAAC,GAAG,YAAY,GAAGC,CAAC;IACxDE,CAAC,EAAE,YAAY,GAAGN,CAAC,GAAG,WAAW,GAAGG,CAAC,GAAG,YAAY,GAAGC,CAAC;IACxDN,CAAC,EAAE,YAAY,GAAGE,CAAC,GAAG,YAAY,GAAGG,CAAC,GAAG,WAAW,GAAGC,CAAC;IACxDL;EACF,CAAC;AACH;AAEA,SAASQ,iBAAiBA,CAACC,GAAa,EAAE;EACxC,SAAS;;EACT,MAAMC,SAAS,GAAGf,IAAI,CAACgB,OAAO,CAACC,OAAO,CAACH,GAAG,CAAC;EAC3C,MAAMI,MAAM,GAAGjB,kBAAkB,CAACc,SAAS,CAAC;EAC5C,IAAID,GAAG,CAACZ,CAAC,KAAKY,GAAG,CAACV,CAAC,IAAIU,GAAG,CAACV,CAAC,KAAKU,GAAG,CAACX,CAAC,EAAE;IACtCe,MAAM,CAACN,CAAC,GAAGM,MAAM,CAACd,CAAC,GAAG,CAAC;EACzB;EACA,OAAOc,MAAM;AACf;AAEA,SAASC,kBAAkBA,CAAC;EAC1BR,CAAC,GAAG,CAAC;EACLC,CAAC,GAAG,CAAC;EACLR,CAAC,GAAG,CAAC;EACLC;AACQ,CAAC,EAAY;EACrB,SAAS;;EACT;EACA,MAAMC,CAAC,GAAGC,IAAI,CAACa,GAAG,CAChBT,CAAC,GAAG,sBAAsB,GACxB,sBAAsB,GAAGC,CAAC,GAC1B,sBAAsB,GAAGR,CAAC,EAC5B,CACF,CAAC;EACD,MAAMK,CAAC,GAAGF,IAAI,CAACa,GAAG;EAChB;EACAT,CAAC,GAAG,qBAAqB,GACvB,qBAAqB,GAAGC,CAAC,GACzB,uBAAuB,GAAGR,CAAC,EAC7B,CACF,CAAC;EACD,MAAMM,CAAC,GAAGH,IAAI,CAACa,GAAG;EAChB;EACAT,CAAC,GAAG,qBAAqB,GACvB,uBAAuB,GAAGC,CAAC,GAC3B,qBAAqB,GAAGR,CAAC,EAC3B,CACF,CAAC;EACD;;EAEA,OAAO;IACLF,CAAC,EAAE,CAAC,iBAAiB,GAAGI,CAAC,GAAG,iBAAiB,GAAGG,CAAC,GAAG,iBAAiB,GAAGC,CAAC;IACzEP,CAAC,EACC,CAAC,kBAAkB,GAAGG,CAAC,GAAG,kBAAkB,GAAGG,CAAC,GAAG,kBAAkB,GAAGC,CAAC;IAC3EN,CAAC,EACC,CAAC,oBAAoB,GAAGE,CAAC,GACzB,kBAAkB,GAAGG,CAAC,GACtB,kBAAkB,GAAGC,CAAC;IACxBL;EACF,CAAC;AACH;AAEA,SAASgB,iBAAiBA,CAACC,QAAkB,EAAY;EACvD,SAAS;;EACT,MAAMC,YAAY,GAAIC,OAAe,IACnCjB,IAAI,CAACkB,IAAI,CAACD,OAAO,GAAG,OAAO,CAAC,GAAG,OAAO;EAExC,MAAMT,SAAS,GAAGI,kBAAkB,CAACG,QAAQ,CAAC;EAC9C,MAAMI,QAAQ,GAAG1B,IAAI,CAACgB,OAAO,CAACW,KAAK,CAACZ,SAAS,CAAC;EAC9CW,QAAQ,CAACxB,CAAC,GAAGqB,YAAY,CAACG,QAAQ,CAACxB,CAAC,CAAC;EACrCwB,QAAQ,CAACvB,CAAC,GAAGoB,YAAY,CAACG,QAAQ,CAACvB,CAAC,CAAC;EACrCuB,QAAQ,CAACtB,CAAC,GAAGmB,YAAY,CAACG,QAAQ,CAACtB,CAAC,CAAC;EACrC,OAAOsB,QAAQ;AACjB;AAEA,eAAe;EACbV,OAAO,EAAE;IACPC,OAAO,EAAEJ,iBAAiB;IAC1Bc,KAAK,EAAEN;EACT;AACF,CAAC", "ignoreList": []}