{"version": 3, "names": ["useEffect", "useRef", "isJest", "isWeb", "makeShareable", "areDependenciesEqual", "buildDependencies", "useHandler", "handlers", "dependencies", "initRef", "current", "context", "savedDependencies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb"], "sourceRoot": "../../../src", "sources": ["hook/useHandler.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAGzC,SAASC,MAAM,EAAEC,KAAK,QAAQ,uBAAoB;AAClD,SAASC,aAAa,QAAQ,kBAAe;AAE7C,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,YAAS;;AAmCjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA,OAAO,SAASC,UAAUA,CAIxBC,QAAgD,EAChDC,YAA6B,EACD;EAC5B,MAAMC,OAAO,GAAGT,MAAM,CAA0C,IAAI,CAAC;EACrE,IAAIS,OAAO,CAACC,OAAO,KAAK,IAAI,EAAE;IAC5B,MAAMC,OAAO,GAAGR,aAAa,CAAC,CAAC,CAAY,CAAC;IAC5CM,OAAO,CAACC,OAAO,GAAG;MAChBC,OAAO;MACPC,iBAAiB,EAAE;IACrB,CAAC;EACH;EAEAb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXU,OAAO,CAACC,OAAO,GAAG,IAAI;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IAAEC,OAAO;IAAEC;EAAkB,CAAC,GAAGH,OAAO,CAACC,OAAO;EAEtDF,YAAY,GAAGH,iBAAiB,CAC9BG,YAAY,EACZD,QACF,CAAC;EAED,MAAMM,oBAAoB,GAAG,CAACT,oBAAoB,CAChDI,YAAY,EACZI,iBACF,CAAC;EACDH,OAAO,CAACC,OAAO,CAACE,iBAAiB,GAAGJ,YAAY;EAChD,MAAMM,MAAM,GAAGZ,KAAK,CAAC,CAAC,IAAID,MAAM,CAAC,CAAC;EAElC,OAAO;IAAEU,OAAO;IAAEE,oBAAoB;IAAEC;EAAO,CAAC;AAClD", "ignoreList": []}