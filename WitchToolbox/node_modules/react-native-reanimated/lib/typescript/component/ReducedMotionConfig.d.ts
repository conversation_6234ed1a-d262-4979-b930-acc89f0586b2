import { ReduceMotion } from '../commonTypes';
/**
 * A component that lets you overwrite default reduce motion behavior globally
 * in your application.
 *
 * @param mode - Determines default reduce motion behavior globally in your
 *   application. Configured with {@link ReduceMotion} enum.
 * @see https://docs.swmansion.com/react-native-reanimated/docs/components/ReducedMotionConfig
 */
export declare function ReducedMotionConfig({ mode }: {
    mode: ReduceMotion;
}): null;
//# sourceMappingURL=ReducedMotionConfig.d.ts.map