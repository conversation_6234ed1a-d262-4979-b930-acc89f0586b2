export declare let getViewInfo: (element: any) => {
    viewName: any;
    /**
     * RN uses viewConfig for components for storing different properties of the
     * component(example:
     * https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Components/ScrollView/ScrollViewNativeComponent.js#L24).
     * The name we're looking for is in the field named uiViewClassName.
     */
    viewTag: any;
    viewConfig: any;
};
//# sourceMappingURL=getViewInfo.d.ts.map