{"name": "witchtoolbox", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "start:clear": "expo start --clear", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "tunnel": "expo start --tunnel", "eject": "expo eject", "build:android": "expo build:android", "build:ios": "expo build:ios"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "expo": "~53.0.20", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-haptics": "~14.1.4", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "typescript": "^5.8.3"}, "private": true}