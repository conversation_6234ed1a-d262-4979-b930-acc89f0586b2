import React, { useRef, ReactNode } from 'react';
import { Animated, TouchableOpacity, ViewStyle, StyleProp } from 'react-native';
import hapticFeedback from '../../utils/haptics';

interface AnimatedPressableProps {
  children: ReactNode;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  hapticType?: keyof typeof hapticFeedback;
  disabled?: boolean;
  [key: string]: any;
}

const AnimatedPressable: React.FC<AnimatedPressableProps> = ({
  children,
  onPress,
  style,
  scaleValue = 0.95,
  hapticType = 'light',
  disabled = false,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (disabled) return;
    
    Animated.spring(scaleAnim, {
      toValue: scaleValue,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePressOut = () => {
    if (disabled) return;
    
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 300,
      friction: 10,
    }).start();
  };

  const handlePress = async () => {
    if (disabled || !onPress) return;
    
    // 触觉反馈
    if (hapticType && hapticFeedback[hapticType]) {
      await hapticFeedback[hapticType]();
    }
    
    onPress();
  };

  const animatedStyle = {
    transform: [{ scale: scaleAnim }],
  };

  return (
    <TouchableOpacity
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={1}
      style={style}
      {...props}
    >
      <Animated.View style={animatedStyle}>
        {children}
      </Animated.View>
    </TouchableOpacity>
  );
};

export default AnimatedPressable;
