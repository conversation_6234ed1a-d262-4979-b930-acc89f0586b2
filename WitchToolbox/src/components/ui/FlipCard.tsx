import React, { useRef, useEffect } from 'react';
import { Animated, StyleSheet, TouchableOpacity } from 'react-native';
import hapticFeedback from '../../utils/haptics';

const FlipCard = ({
  frontContent,
  backContent,
  isFlipped = false,
  onFlip,
  style,
  disabled = false,
  autoFlip = false,
  flipDelay = 0,
  ...props
}) => {
  const flipAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (autoFlip) {
      setTimeout(() => {
        flip();
      }, flipDelay);
    }
  }, [autoFlip, flipDelay]);

  useEffect(() => {
    if (isFlipped) {
      flip();
    }
  }, [isFlipped]);

  const flip = () => {
    if (disabled) return;

    // 触觉反馈
    hapticFeedback.medium();

    Animated.spring(flipAnim, {
      toValue: isFlipped ? 0 : 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();

    if (onFlip) {
      onFlip(!isFlipped);
    }
  };

  const frontInterpolate = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const backInterpolate = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '360deg'],
  });

  const frontAnimatedStyle = {
    transform: [{ rotateY: frontInterpolate }],
  };

  const backAnimatedStyle = {
    transform: [{ rotateY: backInterpolate }],
  };

  return (
    <TouchableOpacity
      onPress={flip}
      disabled={disabled}
      activeOpacity={0.8}
      style={[styles.container, style]}
      {...props}
    >
      {/* 正面 */}
      <Animated.View style={[styles.card, styles.front, frontAnimatedStyle]}>
        {frontContent}
      </Animated.View>

      {/* 背面 */}
      <Animated.View style={[styles.card, styles.back, backAnimatedStyle]}>
        {backContent}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  
  card: {
    backfaceVisibility: 'hidden',
  },
  
  front: {
    position: 'relative',
    zIndex: 2,
  },
  
  back: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
});

export default FlipCard;
