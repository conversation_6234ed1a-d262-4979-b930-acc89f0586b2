import React, { ReactNode } from 'react';
import { StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// 导入样式
import Colors from '../../styles/colors';

interface GradientBackgroundProps {
  children?: ReactNode;
  colors?: readonly [string, string, ...string[]];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: StyleProp<ViewStyle>;
  [key: string]: any;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  children,
  colors = Colors.gradients.mystical,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
  style,
  ...props
}) => {
  return (
    <LinearGradient
      colors={colors}
      style={[StyleSheet.absoluteFill, style]}
      start={start}
      end={end}
      {...props}
    >
      {children}
    </LinearGradient>
  );
};

export default GradientBackground;
