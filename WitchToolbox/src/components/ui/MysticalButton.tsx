import React from 'react';
import { Text, StyleSheet, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import AnimatedPressable from './AnimatedPressable';

// 导入样式
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { Spacing, BorderRadius, Shadows } from '../../styles/common';

const MysticalButton = ({
  title,
  onPress,
  variant = 'primary', // primary, secondary, accent, outline
  size = 'medium', // small, medium, large
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon,
  gradient = false,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];
    
    if (disabled) {
      baseStyle.push(styles.disabled);
    } else {
      baseStyle.push(styles[variant]);
    }
    
    if (style) {
      baseStyle.push(style);
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseTextStyle = [styles.text, styles[`${size}Text`]];
    
    if (disabled) {
      baseTextStyle.push(styles.disabledText);
    } else {
      baseTextStyle.push(styles[`${variant}Text`]);
    }
    
    if (textStyle) {
      baseTextStyle.push(textStyle);
    }
    
    return baseTextStyle;
  };

  const renderContent = () => (
    <>
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' ? Colors.primary[400] : Colors.text.primary}
          style={styles.loader}
        />
      )}
      {icon && !loading && icon}
      {title && (
        <Text style={getTextStyle()}>
          {title}
        </Text>
      )}
    </>
  );

  if (gradient && variant === 'primary' && !disabled) {
    return (
      <AnimatedPressable
        onPress={onPress}
        disabled={disabled || loading}
        hapticType="medium"
        {...props}
      >
        <LinearGradient
          colors={Colors.gradients.primary}
          style={getButtonStyle()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {renderContent()}
        </LinearGradient>
      </AnimatedPressable>
    );
  }

  return (
    <AnimatedPressable
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled || loading}
      hapticType="light"
      {...props}
    >
      {renderContent()}
    </AnimatedPressable>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BorderRadius.lg,
    ...Shadows.sm,
  },

  // 尺寸样式
  small: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    minHeight: 36,
  },

  medium: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    minHeight: 44,
  },

  large: {
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    minHeight: 52,
  },

  // 变体样式
  primary: {
    backgroundColor: Colors.primary[500],
  },

  secondary: {
    backgroundColor: Colors.background.card,
    borderWidth: 1,
    borderColor: Colors.primary[400],
  },

  accent: {
    backgroundColor: Colors.accent[500],
  },

  outline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Colors.primary[400],
  },

  disabled: {
    backgroundColor: Colors.neutral[600],
    borderColor: Colors.neutral[600],
  },

  // 文本样式
  text: {
    ...TextStyles.button,
    fontWeight: '600',
  },

  smallText: {
    fontSize: 14,
  },

  mediumText: {
    fontSize: 16,
  },

  largeText: {
    fontSize: 18,
  },

  primaryText: {
    color: Colors.text.primary,
  },

  secondaryText: {
    color: Colors.primary[400],
  },

  accentText: {
    color: Colors.text.primary,
  },

  outlineText: {
    color: Colors.primary[400],
  },

  disabledText: {
    color: Colors.text.tertiary,
  },

  loader: {
    marginRight: Spacing.sm,
  },
});

export default MysticalButton;
