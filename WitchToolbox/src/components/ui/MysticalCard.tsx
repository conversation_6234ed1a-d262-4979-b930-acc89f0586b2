import React, { ReactNode } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle, StyleProp } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// 导入样式
import Colors from '../../styles/colors';
import { CommonStyles, Spacing, BorderRadius, Shadows } from '../../styles/common';

interface MysticalCardProps {
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  gradient?: boolean;
  glowing?: boolean;
  disabled?: boolean;
  [key: string]: any;
}

const MysticalCard: React.FC<MysticalCardProps> = ({
  children,
  style,
  onPress,
  gradient = false,
  glowing = false,
  disabled = false,
  ...props
}) => {
  const cardStyle = [
    styles.card,
    glowing && styles.glowingCard,
    style,
  ];

  const content = (
    <View style={cardStyle} {...props}>
      {children}
    </View>
  );

  if (gradient) {
    const gradientContent = (
      <LinearGradient
        colors={Colors.gradients.primary}
        style={[cardStyle, styles.gradientCard]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {children}
      </LinearGradient>
    );

    if (onPress && !disabled) {
      return (
        <TouchableOpacity onPress={onPress} activeOpacity={0.8} disabled={disabled}>
          {gradientContent}
        </TouchableOpacity>
      );
    }

    return gradientContent;
  }

  if (onPress && !disabled) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8} disabled={disabled}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.primary[600],
    ...Shadows.md,
  },

  glowingCard: {
    ...Shadows.mystical,
    borderColor: Colors.primary[400],
  },

  gradientCard: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
});

export default MysticalCard;
