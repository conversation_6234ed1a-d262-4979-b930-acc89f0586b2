import React, { useEffect, useRef } from 'react';
import { View, Animated, Dimensions, StyleSheet } from 'react-native';
import Colors from '../../styles/colors';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const Particle = ({ delay = 0, duration = 3000 }) => {
  const translateY = useRef(new Animated.Value(screenHeight + 50)).current;
  const translateX = useRef(new Animated.Value(Math.random() * screenWidth)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      // 重置位置
      translateY.setValue(screenHeight + 50);
      translateX.setValue(Math.random() * screenWidth);
      opacity.setValue(0);
      scale.setValue(0);

      // 开始动画
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: -50,
          duration: duration + Math.random() * 2000,
          useNativeDriver: true,
        }),
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 0.6,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        // 动画完成后重新开始
        setTimeout(animate, Math.random() * 2000);
      });
    };

    // 延迟开始
    setTimeout(animate, delay);
  }, [delay, duration]);

  return (
    <Animated.View
      style={[
        styles.particle,
        {
          transform: [
            { translateX },
            { translateY },
            { scale },
          ],
          opacity,
        },
      ]}
    />
  );
};

const ParticleBackground = ({ 
  particleCount = 20, 
  particleColor = Colors.accent[400],
  style 
}) => {
  const particles = Array.from({ length: particleCount }, (_, index) => (
    <Particle
      key={index}
      delay={index * 200}
      duration={3000 + Math.random() * 2000}
    />
  ));

  return (
    <View style={[styles.container, style]} pointerEvents="none">
      {particles}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    overflow: 'hidden',
  },
  
  particle: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.accent[400],
    shadowColor: Colors.accent[400],
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 4,
  },
});

export default ParticleBackground;
