import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// 导入Tab导航器
import TabNavigator from './TabNavigator';

// 导入工具箱页面
import TarotScreen from '../screens/Toolbox/TarotScreen';
import CrystalScreen from '../screens/Toolbox/CrystalScreen';
import HistoryScreen from '../screens/Toolbox/HistoryScreen';
import OracleScreen from '../screens/Toolbox/OracleScreen';
import MeditationScreen from '../screens/Toolbox/MeditationScreen';

// 导入样式
import Colors from '../styles/colors';
import { TextStyles } from '../styles/typography';

const Stack = createStackNavigator();

const StackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.background.secondary,
          borderBottomWidth: 0,
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTitleStyle: {
          ...TextStyles.navigationTitle,
          color: Colors.text.primary,
        },
        headerTitleAlign: 'center',
        headerTintColor: Colors.accent[400],
        cardStyle: {
          backgroundColor: Colors.background.primary,
        },
        gestureEnabled: true,
        gestureDirection: 'horizontal',
      }}
      initialRouteName="MainTabs"
    >
      <Stack.Screen
        name="MainTabs"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />
      
      <Stack.Screen
        name="Tarot"
        component={TarotScreen}
        options={{
          title: '塔罗占卜',
          headerShown: true,
        }}
      />
      
      <Stack.Screen
        name="Crystal"
        component={CrystalScreen}
        options={{
          title: '水晶疗愈',
          headerShown: true,
        }}
      />
      
      <Stack.Screen
        name="History"
        component={HistoryScreen}
        options={{
          title: '占卜历史',
          headerShown: true,
        }}
      />
      
      <Stack.Screen
        name="Oracle"
        component={OracleScreen}
        options={{
          title: '神谕卡',
          headerShown: true,
        }}
      />
      
      <Stack.Screen
        name="Meditation"
        component={MeditationScreen}
        options={{
          title: '冥想引导',
          headerShown: true,
        }}
      />
    </Stack.Navigator>
  );
};

export default StackNavigator;
