import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { StyleSheet, Platform } from 'react-native';

// 导入屏幕组件
import HomeScreen from '../screens/Home/HomeScreen';
import ToolboxScreen from '../screens/Toolbox/ToolboxScreen';
import LearningScreen from '../screens/Learning/LearningScreen';
import PracticeScreen from '../screens/Practice/PracticeScreen';

// 导入样式
import Colors from '../styles/colors';
import { TextStyles } from '../styles/typography';
import { Spacing } from '../styles/common';

const Tab = createBottomTabNavigator();

// Tab图标映射
const getTabIcon = (routeName, focused, color, size) => {
  let iconName;

  switch (routeName) {
    case 'Home':
      iconName = focused ? 'home' : 'home-outline';
      break;
    case 'Toolbox':
      iconName = focused ? 'library' : 'library-outline';
      break;
    case 'Learning':
      iconName = focused ? 'school' : 'school-outline';
      break;
    case 'Practice':
      iconName = focused ? 'fitness' : 'fitness-outline';
      break;
    default:
      iconName = 'help-outline';
  }

  return <Ionicons name={iconName} size={size} color={color} />;
};

// Tab标签映射
const getTabLabel = (routeName) => {
  switch (routeName) {
    case 'Home':
      return '首页';
    case 'Toolbox':
      return '工具箱';
    case 'Learning':
      return '学习';
    case 'Practice':
      return '实践';
    default:
      return routeName;
  }
};

// 自定义Tab Bar背景（毛玻璃效果）
const CustomTabBarBackground = () => {
  return (
    <BlurView
      intensity={80}
      tint="dark"
      style={StyleSheet.absoluteFill}
    />
  );
};

const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        // Tab图标配置
        tabBarIcon: ({ focused, color, size }) =>
          getTabIcon(route.name, focused, color, size),
        
        // Tab标签配置
        tabBarLabel: getTabLabel(route.name),
        
        // 隐藏头部
        headerShown: false,
        
        // Tab Bar样式配置
        tabBarStyle: {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: Platform.OS === 'ios' ? 'transparent' : Colors.background.secondary,
          borderTopWidth: 0,
          elevation: 0,
          height: Platform.OS === 'ios' ? 88 : 68, // iOS考虑安全区域
          paddingBottom: Platform.OS === 'ios' ? 24 : 8,
          paddingTop: 8,
        },
        
        // Tab Bar背景
        tabBarBackground: Platform.OS === 'ios' ? CustomTabBarBackground : undefined,
        
        // 激活状态颜色
        tabBarActiveTintColor: Colors.accent[400],
        
        // 未激活状态颜色
        tabBarInactiveTintColor: Colors.text.tertiary,
        
        // 标签样式
        tabBarLabelStyle: {
          ...TextStyles.caption2,
          fontWeight: '600',
          marginTop: 4,
        },
        
        // 图标样式
        tabBarIconStyle: {
          marginBottom: -4,
        },
        
        // Tab项样式
        tabBarItemStyle: {
          paddingVertical: 4,
        },
        
        // 激活时的额外样式
        tabBarActiveBackgroundColor: 'transparent',
        
        // 禁用Tab Bar的默认阴影
        tabBarHideOnKeyboard: true,
      })}
      
      // 初始路由
      initialRouteName="Home"
      
      // 屏幕切换动画
      screenListeners={{
        tabPress: (e) => {
          // 可以在这里添加触觉反馈或音效
          // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        },
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarTestID: 'home-tab',
        }}
      />
      
      <Tab.Screen
        name="Toolbox"
        component={ToolboxScreen}
        options={{
          tabBarTestID: 'toolbox-tab',
        }}
      />
      
      <Tab.Screen
        name="Learning"
        component={LearningScreen}
        options={{
          tabBarTestID: 'learning-tab',
        }}
      />
      
      <Tab.Screen
        name="Practice"
        component={PracticeScreen}
        options={{
          tabBarTestID: 'practice-tab',
        }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
