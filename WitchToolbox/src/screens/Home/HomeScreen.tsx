import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProp } from '@react-navigation/native';

// 导入样式
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing } from '../../styles/common';

// 导入数据
import dailyInspirationData from '../../data/dailyInspiration.json';

// 导入组件
import ParticleBackground from '../../components/ui/ParticleBackground';
import AnimatedPressable from '../../components/ui/AnimatedPressable';

// 类型定义
interface DailyInspiration {
  id: number;
  text: string;
  author: string;
  category: string;
}

interface HomeScreenProps {
  navigation: NavigationProp<any>;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [dailyInspiration, setDailyInspiration] = useState<DailyInspiration | null>(null);

  // 获取每日灵感
  useEffect(() => {
    const today = new Date().getDate();
    const inspirationIndex = today % dailyInspirationData.inspirations.length;
    setDailyInspiration(dailyInspirationData.inspirations[inspirationIndex]);
  }, []);

  // 刷新每日灵感
  const refreshInspiration = () => {
    const randomIndex = Math.floor(Math.random() * dailyInspirationData.inspirations.length);
    setDailyInspiration(dailyInspirationData.inspirations[randomIndex]);
  };
  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />

      {/* 背景渐变 */}
      <LinearGradient
        colors={Colors.gradients.mystical}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      {/* 粒子背景 */}
      <ParticleBackground particleCount={15} />

      <ScrollView
        style={CommonStyles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* 头部区域 */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>欢迎来到</Text>
          <Text style={styles.appTitle}>女巫工具箱</Text>
          <Text style={styles.subtitle}>探索内在力量，发现生命奥秘</Text>
        </View>
        
        {/* 每日灵感卡片 */}
        {dailyInspiration && (
          <AnimatedPressable
            style={styles.dailyInspirationCard}
            onPress={refreshInspiration}
            hapticType="medium"
          >
            <Text style={styles.cardTitle}>✨ 今日灵感</Text>
            <Text style={styles.inspirationText}>
              "{dailyInspiration.text}"
            </Text>
            <Text style={styles.cardFooter}>— {dailyInspiration.author}</Text>
            <Text style={styles.refreshHint}>轻触刷新</Text>
          </AnimatedPressable>
        )}
        
        {/* 快捷入口 */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>快速开始</Text>
          
          <View style={styles.actionGrid}>
            <AnimatedPressable
              style={styles.actionCard}
              hapticType="light"
              onPress={() => navigation.navigate('Tarot')}
            >
              <Text style={styles.actionIcon}>🔮</Text>
              <Text style={styles.actionTitle}>塔罗占卜</Text>
              <Text style={styles.actionSubtitle}>探索未来</Text>
            </AnimatedPressable>

            <AnimatedPressable
              style={styles.actionCard}
              hapticType="light"
              onPress={() => navigation.navigate('Oracle')}
            >
              <Text style={styles.actionIcon}>🌟</Text>
              <Text style={styles.actionTitle}>神谕卡</Text>
              <Text style={styles.actionSubtitle}>获得指引</Text>
            </AnimatedPressable>

            <AnimatedPressable
              style={styles.actionCard}
              hapticType="light"
              onPress={() => navigation.navigate('Crystal')}
            >
              <Text style={styles.actionIcon}>💎</Text>
              <Text style={styles.actionTitle}>水晶疗愈</Text>
              <Text style={styles.actionSubtitle}>平衡能量</Text>
            </AnimatedPressable>

            <AnimatedPressable
              style={styles.actionCard}
              hapticType="light"
              onPress={() => navigation.navigate('Meditation')}
            >
              <Text style={styles.actionIcon}>🧘‍♀️</Text>
              <Text style={styles.actionTitle}>冥想引导</Text>
              <Text style={styles.actionSubtitle}>内心平静</Text>
            </AnimatedPressable>
          </View>
        </View>
        
        {/* 推荐内容 */}
        <View style={styles.recommendations}>
          <Text style={styles.sectionTitle}>为你推荐</Text>

          <AnimatedPressable style={styles.recommendationCard} hapticType="light">
            <Text style={styles.recommendationTitle}>新月许愿仪式</Text>
            <Text style={styles.recommendationDescription}>
              在新月的能量下，学习如何许下最有力量的愿望
            </Text>
          </AnimatedPressable>

          <AnimatedPressable style={styles.recommendationCard} hapticType="light">
            <Text style={styles.recommendationTitle}>水晶冥想指南</Text>
            <Text style={styles.recommendationDescription}>
              学习如何与水晶建立连接，提升冥想体验
            </Text>
          </AnimatedPressable>
        </View>

        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  welcomeText: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },
  
  appTitle: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.accent,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  dailyInspirationCard: {
    ...CommonStyles.mysticalCard,
    marginBottom: Spacing.lg,
    alignItems: 'center',
  },
  
  cardTitle: {
    ...TextStyles.headline,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },
  
  inspirationText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: Spacing.md,
  },
  
  cardFooter: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    fontStyle: 'italic',
    marginBottom: Spacing.sm,
  },

  refreshHint: {
    ...TextStyles.caption2,
    color: Colors.accent[400],
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  quickActions: {
    marginBottom: Spacing.lg,
  },
  
  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
    fontWeight: '600',
  },
  
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  
  actionCard: {
    ...CommonStyles.card,
    width: '48%',
    alignItems: 'center',
    marginBottom: Spacing.md,
    paddingVertical: Spacing.lg,
  },
  
  actionIcon: {
    fontSize: 32,
    marginBottom: Spacing.sm,
  },
  
  actionTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },
  
  actionSubtitle: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    textAlign: 'center',
  },
  
  recommendations: {
    flex: 1,
  },
  
  recommendationCard: {
    ...CommonStyles.card,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent[400],
  },
  
  recommendationTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },
  
  recommendationDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },

  bottomSpacing: {
    height: 100, // 为底部Tab Bar留出空间
  },
});

export default HomeScreen;
