import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing, BorderRadius } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';

// 导入存储工具
import { storage } from '../../utils/storage';

// 类型定义
interface Course {
  id: string;
  title: string;
  icon: string;
  duration: string;
  chapters: number;
  description: string;
  status: 'completed' | 'in-progress' | 'locked' | 'available';
  type: 'basic' | 'advanced';
  content: Chapter[];
}

interface Chapter {
  id: string;
  title: string;
  content: string;
  keyPoints: string[];
  quiz?: QuizQuestion[];
}

interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

interface UserProgress {
  completedCourses: string[];
  currentCourse?: string;
  currentChapter?: string;
  quizScores: { [courseId: string]: number };
}

// 学习课程数据
const coursesData: Course[] = [
  {
    id: 'tarot-basics',
    title: '塔罗牌入门',
    icon: '🃏',
    duration: '45分钟',
    chapters: 6,
    description: '从零开始学习塔罗牌的基础知识，包括牌面含义和基本牌阵',
    status: 'available',
    type: 'basic',
    content: [
      {
        id: 'chapter-1',
        title: '塔罗牌的历史与起源',
        content: `塔罗牌起源于15世纪的欧洲，最初被称为"Tarocchi"。这套神秘的卡牌系统包含78张牌，分为大阿卡纳（22张）和小阿卡纳（56张）。

大阿卡纳代表人生的重大主题和精神旅程，从愚者（0号牌）开始，到世界（21号牌）结束，象征着一个完整的生命周期。

小阿卡纳则类似于现代扑克牌，分为四个花色：权杖（火元素）、圣杯（水元素）、宝剑（风元素）、星币（土元素）。每个花色包含14张牌：A到10的数字牌，以及侍从、骑士、王后、国王四张宫廷牌。`,
        keyPoints: [
          '塔罗牌起源于15世纪欧洲',
          '共78张牌：22张大阿卡纳 + 56张小阿卡纳',
          '大阿卡纳代表人生重大主题',
          '小阿卡纳分为四个元素花色',
          '每个花色有14张牌'
        ],
        quiz: [
          {
            id: 'q1',
            question: '塔罗牌总共有多少张牌？',
            options: ['72张', '78张', '80张', '84张'],
            correctAnswer: 1,
            explanation: '塔罗牌总共有78张牌，包括22张大阿卡纳和56张小阿卡纳。'
          },
          {
            id: 'q2',
            question: '大阿卡纳有多少张牌？',
            options: ['20张', '21张', '22张', '23张'],
            correctAnswer: 2,
            explanation: '大阿卡纳有22张牌，从愚者（0号）到世界（21号）。'
          }
        ]
      },
      {
        id: 'chapter-2',
        title: '大阿卡纳的含义',
        content: `大阿卡纳是塔罗牌的核心，每张牌都代表着人生旅程中的重要阶段和深刻洞察。

愚者（The Fool）- 新的开始，纯真，冒险精神
魔术师（The Magician）- 意志力，创造力，行动力
女祭司（The High Priestess）- 直觉，内在智慧，神秘知识
皇后（The Empress）- 丰饶，母性，创造力
皇帝（The Emperor）- 权威，结构，稳定
教皇（The Hierophant）- 传统，精神指导，学习

这些牌卡不仅仅是占卜工具，更是自我认知和精神成长的镜子。每张牌都蕴含着深刻的象征意义，帮助我们理解生命的不同面向。`,
        keyPoints: [
          '愚者代表新的开始和纯真',
          '魔术师象征意志力和创造力',
          '女祭司代表直觉和内在智慧',
          '皇后象征丰饶和母性能量',
          '皇帝代表权威和稳定结构',
          '教皇象征传统和精神指导'
        ]
      }
    ]
  },
  {
    id: 'oracle-reading',
    title: '神谕卡解读',
    icon: '🌟',
    duration: '30分钟',
    chapters: 4,
    description: '学习如何与神谕卡建立连接，接收宇宙的指引信息',
    status: 'available',
    type: 'basic',
    content: [
      {
        id: 'chapter-1',
        title: '神谕卡的本质',
        content: `神谕卡是一种直觉性的占卜工具，与塔罗牌不同，它们通常包含正面的信息和指引。神谕卡的设计目的是为了提供鼓励、洞察和精神指导。

神谕卡的特点：
- 通常包含美丽的艺术作品和正面信息
- 每张卡都有独特的主题和信息
- 更注重直觉感受而非固定含义
- 适合日常指导和冥想

使用神谕卡时，最重要的是保持开放的心态，相信你的直觉。卡片会反映你内心深处已经知道的答案，帮助你获得清晰的洞察。`,
        keyPoints: [
          '神谕卡提供正面指引和鼓励',
          '注重直觉感受和个人解读',
          '适合日常指导和精神成长',
          '每张卡都有独特的艺术和信息',
          '相信直觉是使用的关键'
        ],
        quiz: [
          {
            id: 'q1',
            question: '神谕卡的主要特点是什么？',
            options: ['提供负面警告', '提供正面指引', '预测未来', '控制命运'],
            correctAnswer: 1,
            explanation: '神谕卡的主要特点是提供正面的指引和鼓励，帮助人们获得精神上的支持。'
          }
        ]
      }
    ]
  },
  {
    id: 'crystal-energy',
    title: '水晶能量学',
    icon: '💎',
    duration: '60分钟',
    chapters: 8,
    description: '了解不同水晶的能量属性，学会选择和使用水晶',
    status: 'available',
    type: 'basic',
    content: [
      {
        id: 'chapter-1',
        title: '水晶的能量原理',
        content: `水晶是地球经过数百万年形成的天然矿物，每种水晶都具有独特的振动频率和能量属性。这些能量可以与我们的身体、情感和精神产生共鸣，帮助我们实现平衡和疗愈。

水晶能量的基本原理：
- 每种水晶都有特定的振动频率
- 水晶可以吸收、储存和释放能量
- 不同颜色的水晶对应不同的脉轮
- 水晶的形状和大小影响能量强度

常见的水晶类型：
- 石英类：清水晶、紫水晶、粉水晶
- 保护类：黑曜石、黑碧玺、赤铁矿
- 疗愈类：绿松石、青金石、月光石`,
        keyPoints: [
          '水晶具有独特的振动频率',
          '可以吸收、储存和释放能量',
          '不同颜色对应不同脉轮',
          '形状和大小影响能量强度',
          '分为石英类、保护类、疗愈类等'
        ],
        quiz: [
          {
            id: 'q1',
            question: '水晶能量的基本原理是什么？',
            options: ['磁场作用', '振动频率', '化学反应', '物理压力'],
            correctAnswer: 1,
            explanation: '水晶能量的基本原理是每种水晶都具有特定的振动频率，可以与人体产生共鸣。'
          }
        ]
      }
    ]
  }
];

const LearningScreen: React.FC = () => {
  const [userProgress, setUserProgress] = useState<UserProgress>({
    completedCourses: [],
    quizScores: {}
  });
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [currentChapter, setCurrentChapter] = useState<number>(0);
  const [showQuiz, setShowQuiz] = useState<boolean>(false);
  const [quizAnswers, setQuizAnswers] = useState<{ [questionId: string]: number }>({});
  const [showCourseModal, setShowCourseModal] = useState<boolean>(false);

  // 加载用户进度
  useEffect(() => {
    loadUserProgress();
  }, []);

  const loadUserProgress = async () => {
    try {
      const progress = await storage.getItem('learning_progress');
      if (progress) {
        setUserProgress(progress);
      }
    } catch (error) {
      console.error('加载学习进度失败:', error);
    }
  };

  const saveUserProgress = async (progress: UserProgress) => {
    try {
      await storage.setItem('learning_progress', progress);
      setUserProgress(progress);
    } catch (error) {
      console.error('保存学习进度失败:', error);
    }
  };

  // 开始学习课程
  const startCourse = (course: Course) => {
    setSelectedCourse(course);
    setCurrentChapter(0);
    setShowCourseModal(true);
  };

  // 完成章节
  const completeChapter = () => {
    if (!selectedCourse) return;

    const currentChapterData = selectedCourse.content[currentChapter];

    if (currentChapterData.quiz && currentChapterData.quiz.length > 0) {
      setShowQuiz(true);
    } else {
      nextChapter();
    }
  };

  // 下一章节
  const nextChapter = () => {
    if (!selectedCourse) return;

    if (currentChapter < selectedCourse.content.length - 1) {
      setCurrentChapter(currentChapter + 1);
      setShowQuiz(false);
      setQuizAnswers({});
    } else {
      // 课程完成
      completeCourse();
    }
  };

  // 完成课程
  const completeCourse = () => {
    if (!selectedCourse) return;

    const newProgress = {
      ...userProgress,
      completedCourses: [...userProgress.completedCourses, selectedCourse.id]
    };

    saveUserProgress(newProgress);
    setShowCourseModal(false);
    setSelectedCourse(null);

    Alert.alert(
      '恭喜！',
      `您已完成《${selectedCourse.title}》课程！`,
      [{ text: '确定', style: 'default' }]
    );
  };

  // 提交测验
  const submitQuiz = () => {
    if (!selectedCourse) return;

    const currentChapterData = selectedCourse.content[currentChapter];
    if (!currentChapterData.quiz) return;

    let correctCount = 0;
    currentChapterData.quiz.forEach(question => {
      if (quizAnswers[question.id] === question.correctAnswer) {
        correctCount++;
      }
    });

    const score = Math.round((correctCount / currentChapterData.quiz.length) * 100);

    if (score >= 70) {
      Alert.alert(
        '测验通过！',
        `您的得分：${score}分\n恭喜通过本章节测验！`,
        [{ text: '继续', onPress: nextChapter }]
      );
    } else {
      Alert.alert(
        '需要重新学习',
        `您的得分：${score}分\n建议重新学习本章节内容后再次测验。`,
        [
          { text: '重新学习', onPress: () => setShowQuiz(false) },
          { text: '继续', onPress: nextChapter }
        ]
      );
    }
  };

  // 计算总体进度
  const calculateProgress = () => {
    const totalCourses = coursesData.length;
    const completedCount = userProgress.completedCourses.length;
    return Math.round((completedCount / totalCourses) * 100);
  };

  // 获取课程状态
  const getCourseStatus = (courseId: string): 'completed' | 'available' => {
    return userProgress.completedCourses.includes(courseId) ? 'completed' : 'available';
  };

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      {/* 背景渐变 */}
      <LinearGradient
        colors={Colors.gradients.mystical as any}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>📚 学习中心</Text>
          <Text style={styles.subtitle}>提升你的占卜技能</Text>
        </View>

        {/* 进度概览 */}
        <MysticalCard style={styles.progressCard}>
          <Text style={styles.progressTitle}>学习进度</Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${calculateProgress()}%` }]} />
          </View>
          <Text style={styles.progressText}>
            已完成 {userProgress.completedCourses.length}/{coursesData.length} 个课程
          </Text>
        </MysticalCard>

        {/* 基础教程 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础教程</Text>

          {coursesData.filter(course => course.type === 'basic').map((course) => {
            const status = getCourseStatus(course.id);
            return (
              <TouchableOpacity
                key={course.id}
                onPress={() => startCourse(course)}
                activeOpacity={0.8}
              >
                <MysticalCard style={styles.courseCard}>
                  <View style={styles.courseHeader}>
                    <Text style={styles.courseIcon}>{course.icon}</Text>
                    <View style={styles.courseInfo}>
                      <Text style={styles.courseTitle}>{course.title}</Text>
                      <Text style={styles.courseDuration}>
                        {course.duration} • {course.chapters}个章节
                      </Text>
                    </View>
                    {status === 'completed' && (
                      <View style={styles.completeBadge}>
                        <Text style={styles.completeText}>✓</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.courseDescription}>
                    {course.description}
                  </Text>
                </MysticalCard>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* 课程学习模态框 */}
      <Modal
        visible={showCourseModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <LinearGradient
            colors={Colors.gradients.mystical as any}
            style={StyleSheet.absoluteFill}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />

          {selectedCourse && (
            <View style={styles.modalContent}>
              {/* 模态框头部 */}
              <View style={styles.modalHeader}>
                <TouchableOpacity
                  onPress={() => setShowCourseModal(false)}
                  style={styles.closeButton}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
                <Text style={styles.modalTitle}>
                  {selectedCourse.icon} {selectedCourse.title}
                </Text>
                <Text style={styles.chapterProgress}>
                  第 {currentChapter + 1} 章 / 共 {selectedCourse.content.length} 章
                </Text>
              </View>

              {!showQuiz ? (
                // 章节内容
                <ScrollView style={styles.chapterContent} showsVerticalScrollIndicator={false}>
                  <MysticalCard style={styles.chapterCard}>
                    <Text style={styles.chapterTitle}>
                      {selectedCourse.content[currentChapter]?.title}
                    </Text>
                    <Text style={styles.chapterText}>
                      {selectedCourse.content[currentChapter]?.content}
                    </Text>

                    {selectedCourse.content[currentChapter]?.keyPoints && (
                      <View style={styles.keyPointsSection}>
                        <Text style={styles.keyPointsTitle}>重点总结：</Text>
                        {selectedCourse.content[currentChapter].keyPoints.map((point, index) => (
                          <Text key={index} style={styles.keyPoint}>
                            • {point}
                          </Text>
                        ))}
                      </View>
                    )}
                  </MysticalCard>
                </ScrollView>
              ) : (
                // 测验内容
                <ScrollView style={styles.chapterContent} showsVerticalScrollIndicator={false}>
                  <MysticalCard style={styles.chapterCard}>
                    <Text style={styles.quizTitle}>章节测验</Text>
                    {selectedCourse.content[currentChapter]?.quiz?.map((question, qIndex) => (
                      <View key={question.id} style={styles.questionContainer}>
                        <Text style={styles.questionText}>
                          {qIndex + 1}. {question.question}
                        </Text>
                        {question.options.map((option, oIndex) => (
                          <TouchableOpacity
                            key={oIndex}
                            style={[
                              styles.optionButton,
                              quizAnswers[question.id] === oIndex && styles.selectedOption
                            ]}
                            onPress={() => setQuizAnswers({
                              ...quizAnswers,
                              [question.id]: oIndex
                            })}
                          >
                            <Text style={[
                              styles.optionText,
                              quizAnswers[question.id] === oIndex && styles.selectedOptionText
                            ]}>
                              {option}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    ))}
                  </MysticalCard>
                </ScrollView>
              )}

              {/* 底部按钮 */}
              <View style={styles.modalFooter}>
                {!showQuiz ? (
                  <MysticalButton
                    title="完成本章"
                    onPress={completeChapter}
                    variant="accent"
                    style={styles.actionButton}
                    textStyle={{}}
                    icon=""
                  />
                ) : (
                  <MysticalButton
                    title="提交测验"
                    onPress={submitQuiz}
                    variant="accent"
                    style={styles.actionButton}
                    textStyle={{}}
                    icon=""
                    disabled={
                      !selectedCourse.content[currentChapter]?.quiz?.every(
                        q => quizAnswers[q.id] !== undefined
                      )
                    }
                  />
                )}
              </View>
            </View>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  
  progressCard: {
    ...CommonStyles.mysticalCard,
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  
  progressTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.md,
  },
  
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: Colors.neutral[700],
    borderRadius: 4,
    marginBottom: Spacing.sm,
  },
  
  progressFill: {
    width: '25%',
    height: '100%',
    backgroundColor: Colors.accent[400],
    borderRadius: 4,
  },
  
  progressText: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
  },
  
  section: {
    marginBottom: Spacing.xl,
  },
  
  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },
  
  courseCard: {
    ...CommonStyles.card,
    marginBottom: Spacing.md,
  },
  
  courseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  courseIcon: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  
  courseInfo: {
    flex: 1,
  },
  
  courseTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },
  
  courseDuration: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
  },
  
  courseDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },
  
  completeBadge: {
    backgroundColor: Colors.status.success,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  completeText: {
    color: Colors.text.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  
  inProgressBadge: {
    backgroundColor: Colors.accent[500],
    borderRadius: 12,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
  },
  
  inProgressText: {
    ...TextStyles.caption2,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  premiumBadge: {
    backgroundColor: Colors.primary[500],
    borderRadius: 12,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
  },
  
  premiumText: {
    ...TextStyles.caption2,
    color: Colors.text.primary,
    fontWeight: '600',
  },

  // 模态框样式
  modalContainer: {
    flex: 1,
  },

  modalContent: {
    flex: 1,
    padding: Spacing.md,
  },

  modalHeader: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
    position: 'relative',
  },

  closeButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.card,
    justifyContent: 'center',
    alignItems: 'center',
  },

  closeButtonText: {
    ...TextStyles.title3,
    color: Colors.text.primary,
  },

  modalTitle: {
    ...TextStyles.title2,
    color: Colors.text.primary,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },

  chapterProgress: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  chapterContent: {
    flex: 1,
  },

  chapterCard: {
    marginBottom: Spacing.lg,
  },

  chapterTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    fontWeight: 'bold',
    marginBottom: Spacing.md,
  },

  chapterText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    lineHeight: 24,
    marginBottom: Spacing.lg,
  },

  keyPointsSection: {
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
  },

  keyPointsTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  keyPoint: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
    lineHeight: 18,
  },

  // 测验样式
  quizTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    fontWeight: 'bold',
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },

  questionContainer: {
    marginBottom: Spacing.lg,
  },

  questionText: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.md,
  },

  optionButton: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginBottom: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.neutral[700],
  },

  selectedOption: {
    backgroundColor: Colors.accent[400],
    borderColor: Colors.accent[300],
  },

  optionText: {
    ...TextStyles.body,
    color: Colors.text.primary,
  },

  selectedOptionText: {
    color: Colors.text.primary,
    fontWeight: '600',
  },

  modalFooter: {
    paddingTop: Spacing.lg,
  },

  actionButton: {
    width: '100%',
  },

  bottomSpacing: {
    height: 100,
  },
});

export default LearningScreen;
