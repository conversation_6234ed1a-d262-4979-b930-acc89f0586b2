import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

// 导入样式
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing } from '../../styles/common';

const PracticeScreen: React.FC = () => {
  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      {/* 背景渐变 */}
      <LinearGradient
        colors={Colors.gradients.mystical}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>🌟 实践指导</Text>
          <Text style={styles.subtitle}>将知识转化为实际行动</Text>
        </View>
        
        {/* 每日实践 */}
        <View style={styles.dailyPracticeCard}>
          <Text style={styles.dailyTitle}>今日实践建议</Text>
          <Text style={styles.dailyText}>
            进行10分钟的晨间冥想，设定今日的意图和目标
          </Text>
          <View style={styles.checkboxContainer}>
            <View style={styles.checkbox}>
              <Text style={styles.checkmark}>✓</Text>
            </View>
            <Text style={styles.checkboxText}>标记为已完成</Text>
          </View>
        </View>
        
        {/* 变现指导 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>变现指导</Text>
          
          <View style={styles.guideCard}>
            <View style={styles.guideHeader}>
              <Text style={styles.guideIcon}>💰</Text>
              <View style={styles.guideInfo}>
                <Text style={styles.guideTitle}>开设在线占卜服务</Text>
                <Text style={styles.guideProgress}>进度: 2/5 步骤</Text>
              </View>
            </View>
            <Text style={styles.guideDescription}>
              学习如何建立个人品牌，设置服务价格，吸引客户
            </Text>
            <View style={styles.stepsList}>
              <View style={styles.stepItem}>
                <View style={styles.stepCompleted}>
                  <Text style={styles.stepCheck}>✓</Text>
                </View>
                <Text style={styles.stepText}>确定服务类型</Text>
              </View>
              <View style={styles.stepItem}>
                <View style={styles.stepCompleted}>
                  <Text style={styles.stepCheck}>✓</Text>
                </View>
                <Text style={styles.stepText}>制定价格策略</Text>
              </View>
              <View style={styles.stepItem}>
                <View style={styles.stepPending}>
                  <Text style={styles.stepNumber}>3</Text>
                </View>
                <Text style={styles.stepText}>建立社交媒体存在</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.guideCard}>
            <View style={styles.guideHeader}>
              <Text style={styles.guideIcon}>📱</Text>
              <View style={styles.guideInfo}>
                <Text style={styles.guideTitle}>自媒体运营策略</Text>
                <Text style={styles.guideProgress}>进度: 0/4 步骤</Text>
              </View>
            </View>
            <Text style={styles.guideDescription}>
              掌握内容创作、粉丝互动和品牌建设的核心技巧
            </Text>
          </View>
        </View>
        
        {/* 实践计划 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>实践计划</Text>
          
          <View style={styles.planCard}>
            <View style={styles.planHeader}>
              <Text style={styles.planIcon}>📅</Text>
              <Text style={styles.planTitle}>21天冥想挑战</Text>
            </View>
            <Text style={styles.planDescription}>
              建立每日冥想习惯，提升直觉力和内在平静
            </Text>
            <View style={styles.planProgress}>
              <Text style={styles.planProgressText}>第7天 / 21天</Text>
              <View style={styles.planProgressBar}>
                <View style={styles.planProgressFill} />
              </View>
            </View>
          </View>
          
          <View style={styles.planCard}>
            <View style={styles.planHeader}>
              <Text style={styles.planIcon}>🃏</Text>
              <Text style={styles.planTitle}>每日一卡练习</Text>
            </View>
            <Text style={styles.planDescription}>
              每天抽取一张塔罗牌，记录感悟和解读
            </Text>
            <View style={styles.planProgress}>
              <Text style={styles.planProgressText}>第12天 / 30天</Text>
              <View style={styles.planProgressBar}>
                <View style={[styles.planProgressFill, { width: '40%' }]} />
              </View>
            </View>
          </View>
        </View>
        
        {/* 社区分享 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>社区分享</Text>
          
          <View style={styles.communityCard}>
            <Text style={styles.communityTitle}>加入我们的学习社群</Text>
            <Text style={styles.communityDescription}>
              与其他学习者交流经验，分享实践心得，获得专业指导
            </Text>
            <View style={styles.communityButton}>
              <Text style={styles.communityButtonText}>立即加入</Text>
            </View>
          </View>
        </View>
        
        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  
  dailyPracticeCard: {
    ...CommonStyles.mysticalCard,
    marginBottom: Spacing.lg,
  },
  
  dailyTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },
  
  dailyText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    lineHeight: 22,
    marginBottom: Spacing.md,
  },
  
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    backgroundColor: Colors.status.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  
  checkmark: {
    color: Colors.text.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  
  checkboxText: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
  },
  
  section: {
    marginBottom: Spacing.xl,
  },
  
  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },
  
  guideCard: {
    ...CommonStyles.card,
    marginBottom: Spacing.md,
  },
  
  guideHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  guideIcon: {
    fontSize: 32,
    marginRight: Spacing.md,
  },
  
  guideInfo: {
    flex: 1,
  },
  
  guideTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },
  
  guideProgress: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
  },
  
  guideDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
    marginBottom: Spacing.md,
  },
  
  stepsList: {
    marginTop: Spacing.sm,
  },
  
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  stepCompleted: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.status.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  
  stepPending: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.neutral[600],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  
  stepCheck: {
    color: Colors.text.primary,
    fontSize: 10,
    fontWeight: 'bold',
  },
  
  stepNumber: {
    color: Colors.text.primary,
    fontSize: 10,
    fontWeight: 'bold',
  },
  
  stepText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
  },
  
  planCard: {
    ...CommonStyles.card,
    marginBottom: Spacing.md,
  },
  
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  
  planIcon: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },
  
  planTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  planDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
    marginBottom: Spacing.md,
  },
  
  planProgress: {
    alignItems: 'flex-end',
  },
  
  planProgressText: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    marginBottom: Spacing.xs,
  },
  
  planProgressBar: {
    width: '100%',
    height: 6,
    backgroundColor: Colors.neutral[700],
    borderRadius: 3,
  },
  
  planProgressFill: {
    width: '33%',
    height: '100%',
    backgroundColor: Colors.accent[400],
    borderRadius: 3,
  },
  
  communityCard: {
    ...CommonStyles.mysticalCard,
    alignItems: 'center',
  },
  
  communityTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  communityDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  
  communityButton: {
    backgroundColor: Colors.accent[500],
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
  },
  
  communityButtonText: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
  },
  
  bottomSpacing: {
    height: 100,
  },
});

export default PracticeScreen;
