import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProp } from '@react-navigation/native';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing, BorderRadius } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';
import GradientBackground from '../../components/ui/GradientBackground';

// 导入数据和工具
import crystalData from '../../data/crystals.json';
import { favoriteCrystals, divinationHistory } from '../../utils/storage';

interface CrystalScreenProps {
  navigation: NavigationProp<any>;
}

const CrystalScreen: React.FC<CrystalScreenProps> = ({ navigation }) => {
  const [selectedIntention, setSelectedIntention] = useState(null);
  const [recommendedCrystal, setRecommendedCrystal] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [showResult, setShowResult] = useState(false);
  const [crystalAnimation] = useState(new Animated.Value(0));

  // 加载收藏列表
  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = async () => {
    const favoriteIds = await favoriteCrystals.getAll();
    setFavorites(favoriteIds);
  };

  // 根据意图推荐水晶
  const recommendCrystal = (intention) => {
    setSelectedIntention(intention);
    
    // 获取推荐的水晶ID
    const recommendedIds = intention.recommendedCrystals;
    const randomId = recommendedIds[Math.floor(Math.random() * recommendedIds.length)];
    
    // 找到对应的水晶
    const crystal = crystalData.crystals.find(c => c.id === randomId);
    
    if (crystal) {
      setRecommendedCrystal(crystal);
      setShowResult(true);
      
      // 启动动画
      crystalAnimation.setValue(0);
      Animated.spring(crystalAnimation, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // 保存到历史记录
      saveToHistory(intention, crystal);
    }
  };

  // 保存到历史记录
  const saveToHistory = async (intention, crystal) => {
    try {
      await divinationHistory.save({
        type: 'crystal',
        intention: intention.name,
        crystal: {
          id: crystal.id,
          name: crystal.name,
          properties: crystal.properties,
        },
      });
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  // 切换收藏状态
  const toggleFavorite = async (crystalId) => {
    try {
      const isFav = favorites.includes(crystalId);
      if (isFav) {
        await favoriteCrystals.remove(crystalId);
        setFavorites(prev => prev.filter(id => id !== crystalId));
        Alert.alert('提示', '已从收藏中移除');
      } else {
        await favoriteCrystals.add(crystalId);
        setFavorites(prev => [...prev, crystalId]);
        Alert.alert('提示', '已添加到收藏');
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error);
    }
  };

  // 重新开始
  const resetSelection = () => {
    setSelectedIntention(null);
    setRecommendedCrystal(null);
    setShowResult(false);
    crystalAnimation.setValue(0);
  };

  // 渲染意图选择
  const renderIntentionSelection = () => (
    <View>
      <Text style={styles.sectionTitle}>选择你的意图</Text>
      <Text style={styles.sectionDescription}>
        根据你当前的需求，选择最符合的意图，我们将为你推荐合适的水晶
      </Text>
      
      <View style={styles.intentionGrid}>
        {crystalData.intentions.map((intention) => (
          <TouchableOpacity
            key={intention.name}
            style={styles.intentionCard}
            onPress={() => recommendCrystal(intention)}
            activeOpacity={0.8}
          >
            <MysticalCard style={styles.intentionCardContent}>
              <Text style={styles.intentionName}>{intention.name}</Text>
              <Text style={styles.intentionDescription}>
                {intention.description}
              </Text>
            </MysticalCard>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // 渲染推荐结果
  const renderRecommendation = () => {
    if (!showResult || !recommendedCrystal) return null;

    const animatedStyle = {
      transform: [
        {
          scale: crystalAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [0.8, 1],
          }),
        },
      ],
      opacity: crystalAnimation,
    };

    const isFavorite = favorites.includes(recommendedCrystal.id);

    return (
      <View style={styles.resultContainer}>
        <Text style={styles.resultTitle}>为你推荐</Text>
        
        <Animated.View style={[styles.crystalContainer, animatedStyle]}>
          <MysticalCard style={styles.crystalCard} glowing>
            <View style={styles.crystalHeader}>
              <View style={styles.crystalNameContainer}>
                <Text style={styles.crystalName}>{recommendedCrystal.name}</Text>
                <Text style={styles.crystalNameEn}>{recommendedCrystal.nameEn}</Text>
              </View>
              <TouchableOpacity
                style={styles.favoriteButton}
                onPress={() => toggleFavorite(recommendedCrystal.id)}
              >
                <Text style={[
                  styles.favoriteIcon,
                  isFavorite && styles.favoriteIconActive
                ]}>
                  {isFavorite ? '💎' : '🤍'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.crystalInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>脉轮:</Text>
                <Text style={styles.infoValue}>{recommendedCrystal.chakra}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>元素:</Text>
                <Text style={styles.infoValue}>{recommendedCrystal.element}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>属性:</Text>
                <Text style={styles.infoValue}>
                  {recommendedCrystal.properties.join(' • ')}
                </Text>
              </View>
            </View>

            <View style={styles.healingSection}>
              <Text style={styles.healingTitle}>疗愈功效</Text>
              <Text style={styles.healingText}>
                <Text style={styles.healingLabel}>情感: </Text>
                {recommendedCrystal.healing.emotional}
              </Text>
              <Text style={styles.healingText}>
                <Text style={styles.healingLabel}>身体: </Text>
                {recommendedCrystal.healing.physical}
              </Text>
              <Text style={styles.healingText}>
                <Text style={styles.healingLabel}>精神: </Text>
                {recommendedCrystal.healing.spiritual}
              </Text>
            </View>

            <View style={styles.usageSection}>
              <Text style={styles.usageTitle}>使用方法</Text>
              {recommendedCrystal.usage.map((usage, index) => (
                <Text key={index} style={styles.usageText}>
                  • {usage}
                </Text>
              ))}
            </View>

            <View style={styles.meditationSection}>
              <Text style={styles.meditationTitle}>冥想指导</Text>
              <Text style={styles.meditationText}>
                {recommendedCrystal.meditation}
              </Text>
            </View>
          </MysticalCard>
        </Animated.View>

        <View style={styles.actionButtons}>
          <MysticalButton
            title="重新选择"
            onPress={resetSelection}
            variant="outline"
            style={styles.actionButton}
          />
          <MysticalButton
            title="开始冥想"
            onPress={() => Alert.alert('冥想指导', recommendedCrystal.meditation)}
            variant="accent"
            style={styles.actionButton}
          />
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      <GradientBackground />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>💎 水晶疗愈</Text>
          <Text style={styles.subtitle}>选择适合你的水晶伙伴</Text>
        </View>

        {!showResult ? renderIntentionSelection() : renderRecommendation()}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.sm,
    fontWeight: '600',
    textAlign: 'center',
  },

  sectionDescription: {
    ...TextStyles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    lineHeight: 22,
  },

  intentionGrid: {
    gap: Spacing.md,
  },

  intentionCard: {
    marginBottom: Spacing.sm,
  },

  intentionCardContent: {
    padding: Spacing.lg,
    alignItems: 'center',
  },

  intentionName: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  intentionDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 18,
  },

  resultContainer: {
    flex: 1,
  },

  resultTitle: {
    ...TextStyles.title2,
    color: Colors.accent[400],
    textAlign: 'center',
    marginBottom: Spacing.lg,
    fontWeight: 'bold',
  },

  crystalContainer: {
    marginBottom: Spacing.lg,
  },

  crystalCard: {
    padding: Spacing.lg,
  },

  crystalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  crystalNameContainer: {
    flex: 1,
  },

  crystalName: {
    ...TextStyles.title2,
    color: Colors.text.primary,
    fontWeight: 'bold',
  },

  crystalNameEn: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },

  favoriteButton: {
    padding: Spacing.sm,
  },

  favoriteIcon: {
    fontSize: 24,
  },

  favoriteIconActive: {
    transform: [{ scale: 1.2 }],
  },

  crystalInfo: {
    marginBottom: Spacing.md,
  },

  infoRow: {
    flexDirection: 'row',
    marginBottom: Spacing.xs,
  },

  infoLabel: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    width: 60,
  },

  infoValue: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    flex: 1,
  },

  healingSection: {
    marginBottom: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
  },

  healingTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  healingText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
    lineHeight: 18,
  },

  healingLabel: {
    color: Colors.text.primary,
    fontWeight: '600',
  },

  usageSection: {
    marginBottom: Spacing.md,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
  },

  usageTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  usageText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
    lineHeight: 18,
  },

  meditationSection: {
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
  },

  meditationTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  meditationText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
    fontStyle: 'italic',
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },

  actionButton: {
    flex: 1,
  },

  bottomSpacing: {
    height: 100,
  },
});

export default CrystalScreen;
