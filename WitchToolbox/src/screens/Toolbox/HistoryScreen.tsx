import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';
import GradientBackground from '../../components/ui/GradientBackground';

// 导入工具
import { divinationHistory } from '../../utils/storage';

interface HistoryScreenProps {
  navigation: NavigationProp<any>;
}

const HistoryScreen: React.FC<HistoryScreenProps> = ({ navigation }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = async () => {
    try {
      const records = await divinationHistory.getAll();
      setHistory(records);
    } catch (error) {
      console.error('加载历史记录失败:', error);
      Alert.alert('错误', '加载历史记录失败');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHistory();
    setRefreshing(false);
  };

  const deleteRecord = async (recordId) => {
    Alert.alert(
      '确认删除',
      '确定要删除这条记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await divinationHistory.delete(recordId);
              await loadHistory();
              Alert.alert('提示', '记录已删除');
            } catch (error) {
              console.error('删除记录失败:', error);
              Alert.alert('错误', '删除记录失败');
            }
          },
        },
      ]
    );
  };

  const clearAllHistory = async () => {
    Alert.alert(
      '确认清空',
      '确定要清空所有历史记录吗？此操作不可恢复。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清空',
          style: 'destructive',
          onPress: async () => {
            try {
              await divinationHistory.clear();
              setHistory([]);
              Alert.alert('提示', '历史记录已清空');
            } catch (error) {
              console.error('清空历史记录失败:', error);
              Alert.alert('错误', '清空历史记录失败');
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'tarot':
        return '🃏';
      case 'crystal':
        return '💎';
      case 'oracle':
        return '🌟';
      case 'numerology':
        return '🔢';
      default:
        return '🔮';
    }
  };

  const getTypeName = (type) => {
    switch (type) {
      case 'tarot':
        return '塔罗占卜';
      case 'crystal':
        return '水晶疗愈';
      case 'oracle':
        return '神谕卡';
      case 'numerology':
        return '数字占卜';
      default:
        return '占卜';
    }
  };

  const renderHistoryItem = (record) => (
    <MysticalCard key={record.id} style={styles.historyCard}>
      <View style={styles.historyHeader}>
        <View style={styles.historyTypeContainer}>
          <Text style={styles.historyIcon}>{getTypeIcon(record.type)}</Text>
          <View style={styles.historyInfo}>
            <Text style={styles.historyType}>{getTypeName(record.type)}</Text>
            <Text style={styles.historyDate}>{formatDate(record.timestamp)}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteRecord(record.id)}
        >
          <Text style={styles.deleteIcon}>🗑️</Text>
        </TouchableOpacity>
      </View>

      {record.question && (
        <View style={styles.questionContainer}>
          <Text style={styles.questionLabel}>问题:</Text>
          <Text style={styles.questionText}>{record.question}</Text>
        </View>
      )}

      {record.intention && (
        <View style={styles.intentionContainer}>
          <Text style={styles.intentionLabel}>意图:</Text>
          <Text style={styles.intentionText}>{record.intention}</Text>
        </View>
      )}

      {record.cards && record.cards.length > 0 && (
        <View style={styles.cardsContainer}>
          <Text style={styles.cardsLabel}>抽取的牌:</Text>
          {record.cards.map((card, index) => (
            <Text key={index} style={styles.cardText}>
              • {card.position?.name || `第${index + 1}张`}: {card.isReversed ? '逆位 ' : ''}{card.name}
            </Text>
          ))}
        </View>
      )}

      {record.crystal && (
        <View style={styles.crystalContainer}>
          <Text style={styles.crystalLabel}>推荐水晶:</Text>
          <Text style={styles.crystalText}>{record.crystal.name}</Text>
          <Text style={styles.crystalProperties}>
            属性: {record.crystal.properties?.join(' • ')}
          </Text>
        </View>
      )}
    </MysticalCard>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🔮</Text>
      <Text style={styles.emptyTitle}>暂无历史记录</Text>
      <Text style={styles.emptyDescription}>
        开始你的第一次占卜，记录将会显示在这里
      </Text>
      <MysticalButton
        title="开始占卜"
        onPress={() => navigation.navigate('MainTabs', { screen: 'Toolbox' })}
        variant="accent"
        style={styles.emptyButton}
      />
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={CommonStyles.safeArea}>
        <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
        <GradientBackground />
        <View style={CommonStyles.centerContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      <GradientBackground />
      
      <View style={CommonStyles.content}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>📜 占卜历史</Text>
          <Text style={styles.subtitle}>回顾你的神秘之旅</Text>
        </View>

        {history.length === 0 ? (
          renderEmptyState()
        ) : (
          <>
            {/* 操作按钮 */}
            <View style={styles.actionContainer}>
              <MysticalButton
                title="清空历史"
                onPress={clearAllHistory}
                variant="outline"
                size="small"
                style={styles.clearButton}
              />
            </View>

            {/* 历史记录列表 */}
            <ScrollView
              style={styles.historyList}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  tintColor={Colors.accent[400]}
                />
              }
            >
              {history.map(renderHistoryItem)}
              <View style={styles.bottomSpacing} />
            </ScrollView>
          </>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  loadingText: {
    ...TextStyles.body,
    color: Colors.text.secondary,
  },

  actionContainer: {
    alignItems: 'flex-end',
    marginBottom: Spacing.md,
  },

  clearButton: {
    paddingHorizontal: Spacing.lg,
  },

  historyList: {
    flex: 1,
  },

  historyCard: {
    marginBottom: Spacing.md,
  },

  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  historyTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  historyIcon: {
    fontSize: 24,
    marginRight: Spacing.sm,
  },

  historyInfo: {
    flex: 1,
  },

  historyType: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
  },

  historyDate: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
  },

  deleteButton: {
    padding: Spacing.sm,
  },

  deleteIcon: {
    fontSize: 18,
  },

  questionContainer: {
    marginBottom: Spacing.sm,
  },

  questionLabel: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  questionText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },

  intentionContainer: {
    marginBottom: Spacing.sm,
  },

  intentionLabel: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  intentionText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
  },

  cardsContainer: {
    marginBottom: Spacing.sm,
  },

  cardsLabel: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  cardText: {
    ...TextStyles.caption2,
    color: Colors.text.secondary,
    marginBottom: Spacing.xs,
  },

  crystalContainer: {
    marginBottom: Spacing.sm,
  },

  crystalLabel: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  crystalText: {
    ...TextStyles.footnote,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  crystalProperties: {
    ...TextStyles.caption2,
    color: Colors.text.secondary,
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },

  emptyIcon: {
    fontSize: 64,
    marginBottom: Spacing.lg,
  },

  emptyTitle: {
    ...TextStyles.title3,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  emptyDescription: {
    ...TextStyles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 22,
  },

  emptyButton: {
    paddingHorizontal: Spacing.xl,
  },

  bottomSpacing: {
    height: 100,
  },
});

export default HistoryScreen;
