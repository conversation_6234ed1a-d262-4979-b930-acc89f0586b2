import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';
import GradientBackground from '../../components/ui/GradientBackground';

// 类型定义
interface MeditationSession {
  id: number;
  title: string;
  duration: string;
  description: string;
  type: string;
  instructions: string[];
}

interface MeditationScreenProps {
  navigation: NavigationProp<any>;
}

// 冥想会话数据
const meditationSessions: MeditationSession[] = [
  {
    id: 1,
    title: "基础呼吸冥想",
    duration: "5分钟",
    description: "通过专注呼吸来平静心灵，适合初学者",
    type: "呼吸冥想",
    instructions: [
      "找一个安静舒适的地方坐下",
      "闭上眼睛，放松身体",
      "专注于自然的呼吸",
      "当思绪飘散时，温和地将注意力拉回呼吸",
      "保持5分钟的专注呼吸"
    ]
  },
  {
    id: 2,
    title: "水晶能量冥想",
    duration: "10分钟",
    description: "与水晶建立连接，感受其疗愈能量",
    type: "能量冥想",
    instructions: [
      "选择一块你喜欢的水晶",
      "将水晶放在手心或面前",
      "深呼吸，感受水晶的能量",
      "想象水晶的光芒包围着你",
      "让水晶的能量流入你的身体",
      "感恩水晶的疗愈力量"
    ]
  },
  {
    id: 3,
    title: "脉轮平衡冥想",
    duration: "15分钟",
    description: "平衡七个主要脉轮，恢复能量流动",
    type: "脉轮冥想",
    instructions: [
      "躺下或舒适地坐着",
      "从根轮开始，想象红色光芒",
      "逐一观想每个脉轮的颜色和位置",
      "感受能量在脊柱中流动",
      "在每个脉轮停留约2分钟",
      "最后感受整体的平衡与和谐"
    ]
  },
  {
    id: 4,
    title: "月亮能量冥想",
    duration: "12分钟",
    description: "连接月亮的神秘能量，增强直觉力",
    type: "月亮冥想",
    instructions: [
      "在月光下或想象月光照射",
      "深呼吸，感受月亮的柔和能量",
      "想象银色光芒从头顶流入",
      "让月亮能量净化你的心灵",
      "设定意图或许下心愿",
      "感恩月亮女神的祝福"
    ]
  }
];

const MeditationScreen: React.FC<MeditationScreenProps> = ({ navigation }) => {
  const [selectedSession, setSelectedSession] = useState<MeditationSession | null>(null);
  const [isSessionActive, setIsSessionActive] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [timer, setTimer] = useState<number>(0);

  // 开始冥想会话
  const startSession = (session: MeditationSession) => {
    setSelectedSession(session);
    setIsSessionActive(true);
    setCurrentStep(0);
    setTimer(0);
  };

  // 结束冥想会话
  const endSession = () => {
    setSelectedSession(null);
    setIsSessionActive(false);
    setCurrentStep(0);
    setTimer(0);
    Alert.alert('冥想完成', '感谢您完成这次冥想练习。愿您内心平静，充满爱与光明。');
  };

  // 下一步
  const nextStep = () => {
    if (selectedSession && currentStep < selectedSession.instructions.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      endSession();
    }
  };

  // 渲染冥想会话列表
  const renderSessionList = () => (
    <View>
      <Text style={styles.sectionTitle}>选择冥想练习</Text>
      <Text style={styles.sectionDescription}>
        选择适合您当前状态的冥想练习，让心灵获得平静与治愈
      </Text>
      
      {meditationSessions.map((session) => (
        <TouchableOpacity
          key={session.id}
          onPress={() => startSession(session)}
          activeOpacity={0.8}
        >
          <MysticalCard style={styles.sessionCard}>
            <View style={styles.sessionHeader}>
              <Text style={styles.sessionTitle}>{session.title}</Text>
              <Text style={styles.sessionDuration}>{session.duration}</Text>
            </View>
            <Text style={styles.sessionType}>{session.type}</Text>
            <Text style={styles.sessionDescription}>{session.description}</Text>
          </MysticalCard>
        </TouchableOpacity>
      ))}
    </View>
  );

  // 渲染冥想指导
  const renderMeditationGuide = () => {
    if (!selectedSession || !isSessionActive) return null;

    return (
      <View style={styles.guideContainer}>
        <Text style={styles.guideTitle}>{selectedSession.title}</Text>
        <Text style={styles.guideProgress}>
          步骤 {currentStep + 1} / {selectedSession.instructions.length}
        </Text>
        
        <MysticalCard style={styles.instructionCard} glowing>
          <Text style={styles.instructionText}>
            {selectedSession.instructions[currentStep]}
          </Text>
        </MysticalCard>

        <View style={styles.guideButtons}>
          <MysticalButton
            title="结束冥想"
            onPress={endSession}
            variant="outline"
            style={styles.guideButton}
          />
          <MysticalButton
            title={currentStep === selectedSession.instructions.length - 1 ? "完成" : "下一步"}
            onPress={nextStep}
            variant="accent"
            style={styles.guideButton}
          />
        </View>

        <View style={styles.tipContainer}>
          <Text style={styles.tipText}>
            💡 提示：如果思绪飘散，这是正常的。温和地将注意力拉回当下即可。
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      <GradientBackground />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>🧘‍♀️ 冥想引导</Text>
          <Text style={styles.subtitle}>找到内心的平静与智慧</Text>
        </View>

        {!isSessionActive ? renderSessionList() : renderMeditationGuide()}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.sm,
    fontWeight: '600',
    textAlign: 'center',
  },

  sectionDescription: {
    ...TextStyles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    lineHeight: 22,
  },

  sessionCard: {
    marginBottom: Spacing.md,
  },

  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },

  sessionTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    flex: 1,
  },

  sessionDuration: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
  },

  sessionType: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    marginBottom: Spacing.sm,
  },

  sessionDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },

  guideContainer: {
    flex: 1,
  },

  guideTitle: {
    ...TextStyles.title2,
    color: Colors.accent[400],
    textAlign: 'center',
    marginBottom: Spacing.sm,
    fontWeight: 'bold',
  },

  guideProgress: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },

  instructionCard: {
    marginBottom: Spacing.lg,
    padding: Spacing.xl,
    alignItems: 'center',
  },

  instructionText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 18,
  },

  guideButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },

  guideButton: {
    flex: 1,
  },

  tipContainer: {
    backgroundColor: Colors.background.card,
    borderRadius: 12,
    padding: Spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent[400],
  },

  tipText: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },

  bottomSpacing: {
    height: 100,
  },
});

export default MeditationScreen;
