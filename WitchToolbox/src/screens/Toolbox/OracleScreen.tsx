import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProp } from '@react-navigation/native';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing, BorderRadius } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';
import GradientBackground from '../../components/ui/GradientBackground';

// 导入数据和工具
import oracleData from '../../data/oracle.json';
import { divinationHistory } from '../../utils/storage';

// 类型定义
interface OracleCard {
  id: number;
  name: string;
  nameEn: string;
  message: string;
  keywords: string[];
  element: string;
  chakra: string;
  affirmation: string;
}

interface OracleScreenProps {
  navigation: NavigationProp<any>;
}

const OracleScreen: React.FC<OracleScreenProps> = ({ navigation }) => {
  const [selectedCard, setSelectedCard] = useState<OracleCard | null>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const [showResult, setShowResult] = useState<boolean>(false);
  const [cardAnimation] = useState(new Animated.Value(0));

  // 随机抽取神谕卡
  const drawCard = async () => {
    setIsDrawing(true);
    setShowResult(false);

    // 模拟抽卡延迟
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * oracleData.oracleCards.length);
      const card = oracleData.oracleCards[randomIndex];
      
      setSelectedCard(card);
      setIsDrawing(false);
      setShowResult(true);

      // 启动卡片动画
      cardAnimation.setValue(0);
      Animated.spring(cardAnimation, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // 保存到历史记录
      saveToHistory(card);
    }, 2000);
  };

  // 保存到历史记录
  const saveToHistory = async (card: OracleCard) => {
    try {
      await divinationHistory.save({
        type: 'oracle',
        card: {
          id: card.id,
          name: card.name,
          message: card.message,
          keywords: card.keywords,
        },
      });
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  // 重新开始
  const resetReading = () => {
    setSelectedCard(null);
    setShowResult(false);
    setIsDrawing(false);
    cardAnimation.setValue(0);
  };

  // 渲染抽卡界面
  const renderDrawInterface = () => (
    <View style={styles.drawContainer}>
      <MysticalCard style={styles.instructionCard}>
        <Text style={styles.instructionTitle}>神谕卡指引</Text>
        <Text style={styles.instructionText}>
          静下心来，专注于你想要获得指引的问题。神谕卡将为你带来来自宇宙的智慧和启示。
        </Text>
        <Text style={styles.instructionSteps}>
          1. 深呼吸，让心灵平静{'\n'}
          2. 在心中默念你的问题{'\n'}
          3. 点击下方按钮抽取神谕卡
        </Text>
      </MysticalCard>

      <MysticalButton
        title={isDrawing ? "正在连接宇宙..." : "抽取神谕卡"}
        onPress={drawCard}
        loading={isDrawing}
        gradient={true}
        size="large"
        style={styles.drawButton}
        disabled={isDrawing}
      />
    </View>
  );

  // 渲染卡片结果
  const renderCardResult = () => {
    if (!showResult || !selectedCard) return null;

    const animatedStyle = {
      transform: [
        {
          scale: cardAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [0.8, 1],
          }),
        },
      ],
      opacity: cardAnimation,
    };

    return (
      <View style={styles.resultContainer}>
        <Text style={styles.resultTitle}>你的神谕卡</Text>
        
        <Animated.View style={[styles.cardContainer, animatedStyle]}>
          <MysticalCard style={styles.oracleCard} glowing>
            <View style={styles.cardHeader}>
              <Text style={styles.cardName}>{selectedCard.name}</Text>
              <Text style={styles.cardNameEn}>{selectedCard.nameEn}</Text>
            </View>

            <View style={styles.cardContent}>
              <Text style={styles.cardMessage}>{selectedCard.message}</Text>
            </View>

            <View style={styles.cardDetails}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>元素:</Text>
                <Text style={styles.detailValue}>{selectedCard.element}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>脉轮:</Text>
                <Text style={styles.detailValue}>{selectedCard.chakra}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>关键词:</Text>
                <Text style={styles.detailValue}>
                  {selectedCard.keywords.join(' • ')}
                </Text>
              </View>
            </View>

            <View style={styles.affirmationSection}>
              <Text style={styles.affirmationTitle}>正面肯定语</Text>
              <Text style={styles.affirmationText}>
                "{selectedCard.affirmation}"
              </Text>
            </View>
          </MysticalCard>
        </Animated.View>

        <View style={styles.actionButtons}>
          <MysticalButton
            title="重新抽卡"
            onPress={resetReading}
            variant="outline"
            style={styles.actionButton}
          />
          <MysticalButton
            title="冥想这张卡"
            onPress={() => Alert.alert('冥想指导', `专注于"${selectedCard.name}"的能量，重复肯定语：${selectedCard.affirmation}`)}
            variant="accent"
            style={styles.actionButton}
          />
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      <GradientBackground />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>🌟 神谕卡</Text>
          <Text style={styles.subtitle}>接收宇宙的神圣指引</Text>
        </View>

        {!showResult ? renderDrawInterface() : renderCardResult()}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  drawContainer: {
    flex: 1,
    justifyContent: 'center',
  },

  instructionCard: {
    marginBottom: Spacing.xl,
    alignItems: 'center',
  },

  instructionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  instructionText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: Spacing.md,
  },

  instructionSteps: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  drawButton: {
    marginBottom: Spacing.xl,
  },

  resultContainer: {
    flex: 1,
  },

  resultTitle: {
    ...TextStyles.title2,
    color: Colors.accent[400],
    textAlign: 'center',
    marginBottom: Spacing.lg,
    fontWeight: 'bold',
  },

  cardContainer: {
    marginBottom: Spacing.lg,
  },

  oracleCard: {
    padding: Spacing.lg,
  },

  cardHeader: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },

  cardName: {
    ...TextStyles.title2,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },

  cardNameEn: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },

  cardContent: {
    marginBottom: Spacing.lg,
  },

  cardMessage: {
    ...TextStyles.body,
    color: Colors.text.primary,
    lineHeight: 24,
    textAlign: 'center',
  },

  cardDetails: {
    marginBottom: Spacing.lg,
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
  },

  detailRow: {
    flexDirection: 'row',
    marginBottom: Spacing.sm,
  },

  detailLabel: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    width: 80,
  },

  detailValue: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    flex: 1,
  },

  affirmationSection: {
    paddingTop: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
    alignItems: 'center',
  },

  affirmationTitle: {
    ...TextStyles.callout,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.sm,
  },

  affirmationText: {
    ...TextStyles.body,
    color: Colors.text.primary,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 22,
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },

  actionButton: {
    flex: 1,
  },

  bottomSpacing: {
    height: 100,
  },
});

export default OracleScreen;
