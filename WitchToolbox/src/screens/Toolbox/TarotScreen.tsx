import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProp } from '@react-navigation/native';

// 导入样式和组件
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing, BorderRadius } from '../../styles/common';
import MysticalCard from '../../components/ui/MysticalCard';
import MysticalButton from '../../components/ui/MysticalButton';
import GradientBackground from '../../components/ui/GradientBackground';

// 导入数据和工具
import tarotData from '../../data/tarot.json';
import { divinationHistory } from '../../utils/storage';

interface TarotScreenProps {
  navigation: NavigationProp<any>;
}

const TarotScreen: React.FC<TarotScreenProps> = ({ navigation }) => {
  const [selectedSpread, setSelectedSpread] = useState('single');
  const [question, setQuestion] = useState('');
  const [drawnCards, setDrawnCards] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [cardAnimations, setCardAnimations] = useState([]);

  // 初始化动画值
  useEffect(() => {
    const spread = tarotData.spreads.find(s => s.id === selectedSpread);
    if (spread) {
      const animations = spread.positions.map(() => new Animated.Value(0));
      setCardAnimations(animations);
    }
  }, [selectedSpread]);

  // 获取当前牌阵
  const getCurrentSpread = () => {
    return tarotData.spreads.find(s => s.id === selectedSpread);
  };

  // 随机抽牌
  const drawCards = async () => {
    if (!question.trim()) {
      Alert.alert('提示', '请先输入你的问题');
      return;
    }

    setIsDrawing(true);
    setShowResult(false);

    const spread = getCurrentSpread();
    const numberOfCards = spread.positions.length;
    
    // 模拟洗牌延迟
    setTimeout(() => {
      // 随机选择牌
      const shuffledCards = [...tarotData.majorArcana].sort(() => Math.random() - 0.5);
      const selectedCards = shuffledCards.slice(0, numberOfCards);
      
      // 为每张牌随机决定正位或逆位
      const cardsWithOrientation = selectedCards.map((card, index) => ({
        ...card,
        position: spread.positions[index],
        isReversed: Math.random() < 0.3, // 30%概率逆位
        positionIndex: index,
      }));

      setDrawnCards(cardsWithOrientation);
      setIsDrawing(false);
      setShowResult(true);

      // 启动卡片翻转动画
      cardsWithOrientation.forEach((_, index) => {
        setTimeout(() => {
          Animated.spring(cardAnimations[index], {
            toValue: 1,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }, index * 300);
      });

      // 保存到历史记录
      saveToHistory(cardsWithOrientation);
    }, 2000);
  };

  // 保存到历史记录
  const saveToHistory = async (cards) => {
    try {
      await divinationHistory.save({
        type: 'tarot',
        question: question.trim(),
        spread: selectedSpread,
        cards: cards.map(card => ({
          id: card.id,
          name: card.name,
          isReversed: card.isReversed,
          position: card.position,
        })),
      });
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  // 重新开始
  const resetReading = () => {
    setQuestion('');
    setDrawnCards([]);
    setShowResult(false);
    setIsDrawing(false);
    cardAnimations.forEach(anim => anim.setValue(0));
  };

  // 渲染牌阵选择
  const renderSpreadSelection = () => (
    <MysticalCard style={styles.spreadCard}>
      <Text style={styles.sectionTitle}>选择牌阵</Text>
      <View style={styles.spreadOptions}>
        {tarotData.spreads.map((spread) => (
          <TouchableOpacity
            key={spread.id}
            style={[
              styles.spreadOption,
              selectedSpread === spread.id && styles.selectedSpread,
            ]}
            onPress={() => setSelectedSpread(spread.id)}
          >
            <Text style={[
              styles.spreadName,
              selectedSpread === spread.id && styles.selectedSpreadText,
            ]}>
              {spread.name}
            </Text>
            <Text style={styles.spreadDescription}>{spread.description}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </MysticalCard>
  );

  // 渲染问题输入
  const renderQuestionInput = () => (
    <MysticalCard style={styles.questionCard}>
      <Text style={styles.sectionTitle}>你的问题</Text>
      <TextInput
        style={styles.questionInput}
        placeholder="请输入你想要询问的问题..."
        placeholderTextColor={Colors.text.tertiary}
        value={question}
        onChangeText={setQuestion}
        multiline
        maxLength={200}
      />
      <Text style={styles.inputHint}>
        专注于你真正关心的问题，让塔罗为你指引方向
      </Text>
    </MysticalCard>
  );

  // 渲染抽牌按钮
  const renderDrawButton = () => (
    <MysticalButton
      title={isDrawing ? "正在洗牌..." : "开始占卜"}
      onPress={drawCards}
      loading={isDrawing}
      gradient={true}
      size="large"
      style={styles.drawButton}
      disabled={!question.trim() || isDrawing}
    />
  );

  // 渲染卡片
  const renderCard = (card, index) => {
    const animatedStyle = {
      transform: [
        {
          rotateY: cardAnimations[index]?.interpolate({
            inputRange: [0, 1],
            outputRange: ['180deg', '0deg'],
          }) || '180deg',
        },
        {
          scale: cardAnimations[index]?.interpolate({
            inputRange: [0, 1],
            outputRange: [0.8, 1],
          }) || 0.8,
        },
      ],
      opacity: cardAnimations[index] || 0,
    };

    return (
      <Animated.View key={`${card.id}-${index}`} style={[styles.cardContainer, animatedStyle]}>
        <MysticalCard style={styles.tarotCard} glowing>
          <View style={styles.cardHeader}>
            <Text style={styles.positionName}>{card.position.name}</Text>
            <Text style={styles.cardName}>
              {card.isReversed ? '逆位 ' : ''}{card.name}
            </Text>
          </View>
          
          <View style={styles.cardContent}>
            <Text style={styles.cardMeaning}>
              {card.isReversed ? card.reversed.meaning : card.upright.meaning}
            </Text>
            <Text style={styles.cardDescription}>
              {card.isReversed ? card.reversed.description : card.upright.description}
            </Text>
          </View>
          
          <View style={styles.cardFooter}>
            <Text style={styles.cardKeywords}>
              关键词: {card.keywords.join(' • ')}
            </Text>
          </View>
        </MysticalCard>
      </Animated.View>
    );
  };

  // 渲染结果
  const renderResult = () => {
    if (!showResult || drawnCards.length === 0) return null;

    return (
      <View style={styles.resultContainer}>
        <Text style={styles.resultTitle}>占卜结果</Text>
        <Text style={styles.resultQuestion}>问题: {question}</Text>
        
        <ScrollView style={styles.cardsContainer} showsVerticalScrollIndicator={false}>
          {drawnCards.map((card, index) => renderCard(card, index))}
        </ScrollView>

        <View style={styles.actionButtons}>
          <MysticalButton
            title="重新占卜"
            onPress={resetReading}
            variant="outline"
            style={styles.actionButton}
          />
          <MysticalButton
            title="保存结果"
            onPress={() => Alert.alert('提示', '结果已自动保存到历史记录')}
            variant="accent"
            style={styles.actionButton}
          />
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      <GradientBackground />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>🃏 塔罗占卜</Text>
          <Text style={styles.subtitle}>探索过去、现在与未来</Text>
        </View>

        {!showResult ? (
          <>
            {renderSpreadSelection()}
            {renderQuestionInput()}
            {renderDrawButton()}
          </>
        ) : (
          renderResult()
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },

  spreadCard: {
    marginBottom: Spacing.lg,
  },

  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  spreadOptions: {
    gap: Spacing.sm,
  },

  spreadOption: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.neutral[600],
  },

  selectedSpread: {
    borderColor: Colors.accent[400],
    backgroundColor: Colors.primary[900],
  },

  spreadName: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  selectedSpreadText: {
    color: Colors.accent[400],
  },

  spreadDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
  },

  questionCard: {
    marginBottom: Spacing.lg,
  },

  questionInput: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    color: Colors.text.primary,
    ...TextStyles.body,
    minHeight: 80,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: Colors.neutral[600],
  },

  inputHint: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    marginTop: Spacing.sm,
    fontStyle: 'italic',
  },

  drawButton: {
    marginBottom: Spacing.xl,
  },

  resultContainer: {
    flex: 1,
  },

  resultTitle: {
    ...TextStyles.title2,
    color: Colors.accent[400],
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontWeight: 'bold',
  },

  resultQuestion: {
    ...TextStyles.callout,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    fontStyle: 'italic',
  },

  cardsContainer: {
    flex: 1,
    marginBottom: Spacing.lg,
  },

  cardContainer: {
    marginBottom: Spacing.lg,
  },

  tarotCard: {
    padding: Spacing.lg,
  },

  cardHeader: {
    marginBottom: Spacing.md,
    alignItems: 'center',
  },

  positionName: {
    ...TextStyles.caption1,
    color: Colors.accent[400],
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },

  cardName: {
    ...TextStyles.title3,
    color: Colors.text.primary,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  cardContent: {
    marginBottom: Spacing.md,
  },

  cardMeaning: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },

  cardDescription: {
    ...TextStyles.body,
    color: Colors.text.secondary,
    lineHeight: 22,
    textAlign: 'center',
  },

  cardFooter: {
    borderTopWidth: 1,
    borderTopColor: Colors.neutral[700],
    paddingTop: Spacing.sm,
  },

  cardKeywords: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
    textAlign: 'center',
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },

  actionButton: {
    flex: 1,
  },

  bottomSpacing: {
    height: 100,
  },
});

export default TarotScreen;
