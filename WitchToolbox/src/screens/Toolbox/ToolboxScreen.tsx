import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { NavigationProp } from '@react-navigation/native';

// 导入样式
import Colors from '../../styles/colors';
import { TextStyles } from '../../styles/typography';
import { CommonStyles, Spacing } from '../../styles/common';

interface ToolboxScreenProps {
  navigation: NavigationProp<any>;
}

const ToolboxScreen: React.FC<ToolboxScreenProps> = ({ navigation }) => {
  return (
    <SafeAreaView style={CommonStyles.safeArea}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.background.primary} />
      
      {/* 背景渐变 */}
      <LinearGradient
        colors={Colors.gradients.mystical}
        style={StyleSheet.absoluteFill}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      <ScrollView style={CommonStyles.content} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>🔮 神秘工具箱</Text>
          <Text style={styles.subtitle}>选择你的占卜工具</Text>
        </View>
        
        {/* 基础占卜工具 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础占卜工具</Text>
          
          <TouchableOpacity 
            style={styles.toolCard}
            onPress={() => navigation.navigate('Tarot')}
            activeOpacity={0.8}
          >
            <Text style={styles.toolIcon}>🃏</Text>
            <View style={styles.toolInfo}>
              <Text style={styles.toolTitle}>塔罗牌占卜</Text>
              <Text style={styles.toolDescription}>
                通过78张塔罗牌探索过去、现在和未来
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolCard}
            onPress={() => navigation.navigate('Oracle')}
            activeOpacity={0.8}
          >
            <Text style={styles.toolIcon}>🌟</Text>
            <View style={styles.toolInfo}>
              <Text style={styles.toolTitle}>神谕卡占卜</Text>
              <Text style={styles.toolDescription}>
                获得天使和宇宙的神圣指引
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolCard}
            onPress={() => navigation.navigate('Crystal')}
            activeOpacity={0.8}
          >
            <Text style={styles.toolIcon}>💎</Text>
            <View style={styles.toolInfo}>
              <Text style={styles.toolTitle}>水晶疗愈</Text>
              <Text style={styles.toolDescription}>
                选择适合的水晶，平衡身心能量
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolCard}
            onPress={() => navigation.navigate('Meditation')}
            activeOpacity={0.8}
          >
            <Text style={styles.toolIcon}>🧘‍♀️</Text>
            <View style={styles.toolInfo}>
              <Text style={styles.toolTitle}>冥想引导</Text>
              <Text style={styles.toolDescription}>
                音频引导，进入深度冥想状态
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        
        {/* 其他功能 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>其他功能</Text>
          
          <TouchableOpacity 
            style={styles.toolCard}
            onPress={() => navigation.navigate('History')}
            activeOpacity={0.8}
          >
            <Text style={styles.toolIcon}>📜</Text>
            <View style={styles.toolInfo}>
              <Text style={styles.toolTitle}>占卜历史</Text>
              <Text style={styles.toolDescription}>
                查看和管理你的占卜记录
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        
        {/* 底部间距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  
  title: {
    ...TextStyles.largeTitle,
    color: Colors.text.primary,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...TextStyles.subhead,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  
  section: {
    marginBottom: Spacing.xl,
  },
  
  sectionTitle: {
    ...TextStyles.title3,
    color: Colors.accent[400],
    marginBottom: Spacing.md,
    fontWeight: '600',
  },
  
  toolCard: {
    ...CommonStyles.mysticalCard,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  
  toolIcon: {
    fontSize: 40,
    marginRight: Spacing.md,
  },
  
  toolInfo: {
    flex: 1,
  },
  
  toolTitle: {
    ...TextStyles.callout,
    color: Colors.text.primary,
    fontWeight: '600',
    marginBottom: Spacing.xs,
  },
  
  toolDescription: {
    ...TextStyles.footnote,
    color: Colors.text.secondary,
    lineHeight: 18,
  },
  
  bottomSpacing: {
    height: 100,
  },
});

export default ToolboxScreen;
