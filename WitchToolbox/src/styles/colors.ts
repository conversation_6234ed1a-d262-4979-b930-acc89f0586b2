// iOS现代风格 + 神秘主题颜色配置
export const Colors = {
  // 主要颜色 - 神秘紫色系
  primary: {
    50: '#F3E8FF',   // 最浅紫色
    100: '#E9D5FF',  // 浅紫色
    200: '#D8B4FE',  // 中浅紫色
    300: '#C084FC',  // 中紫色
    400: '#A855F7',  // 主紫色
    500: '#9333EA',  // 深紫色
    600: '#7C3AED',  // 更深紫色
    700: '#6D28D9',  // 深紫色
    800: '#5B21B6',  // 很深紫色
    900: '#4C1D95',  // 最深紫色
  },

  // 辅助颜色 - 金色系（神秘装饰）
  accent: {
    50: '#FFFBEB',   // 最浅金色
    100: '#FEF3C7',  // 浅金色
    200: '#FDE68A',  // 中浅金色
    300: '#FCD34D',  // 中金色
    400: '#FBBF24',  // 主金色
    500: '#F59E0B',  // 深金色
    600: '#D97706',  // 更深金色
    700: '#B45309',  // 深金色
    800: '#92400E',  // 很深金色
    900: '#78350F',  // 最深金色
  },

  // 中性色 - iOS风格灰色系
  neutral: {
    50: '#F9FAFB',   // 最浅灰
    100: '#F3F4F6',  // 浅灰
    200: '#E5E7EB',  // 中浅灰
    300: '#D1D5DB',  // 中灰
    400: '#9CA3AF',  // 深中灰
    500: '#6B7280',  // 深灰
    600: '#4B5563',  // 更深灰
    700: '#374151',  // 深灰
    800: '#1F2937',  // 很深灰
    900: '#111827',  // 最深灰
  },

  // 背景色
  background: {
    primary: '#0F0B1A',      // 深紫黑色主背景
    secondary: '#1A1625',    // 次要背景
    card: 'rgba(255, 255, 255, 0.1)',  // 卡片背景（毛玻璃效果）
    overlay: 'rgba(0, 0, 0, 0.5)',     // 遮罩层
  },

  // 文本颜色
  text: {
    primary: '#FFFFFF',      // 主要文本（白色）
    secondary: '#E5E7EB',    // 次要文本（浅灰）
    tertiary: '#9CA3AF',     // 三级文本（中灰）
    accent: '#FBBF24',       // 强调文本（金色）
    purple: '#C084FC',       // 紫色文本
  },

  // 状态颜色
  status: {
    success: '#10B981',      // 成功绿色
    warning: '#F59E0B',      // 警告橙色
    error: '#EF4444',        // 错误红色
    info: '#3B82F6',         // 信息蓝色
  },

  // 渐变色
  gradients: {
    primary: ['#9333EA', '#4C1D95'],           // 主要紫色渐变
    mystical: ['#0F0B1A', '#1A1625', '#2D1B69'], // 神秘背景渐变
    gold: ['#FBBF24', '#F59E0B'],              // 金色渐变
    aurora: ['#9333EA', '#C084FC', '#FBBF24'], // 极光渐变
  },

  // iOS系统颜色适配
  ios: {
    systemBlue: '#007AFF',
    systemGreen: '#34C759',
    systemIndigo: '#5856D6',
    systemOrange: '#FF9500',
    systemPink: '#FF2D92',
    systemPurple: '#AF52DE',
    systemRed: '#FF3B30',
    systemTeal: '#5AC8FA',
    systemYellow: '#FFCC00',
    
    // iOS标准灰色
    systemGray: '#8E8E93',
    systemGray2: '#AEAEB2',
    systemGray3: '#C7C7CC',
    systemGray4: '#D1D1D6',
    systemGray5: '#E5E5EA',
    systemGray6: '#F2F2F7',
  },
};

// 主题配置
export const Theme = {
  dark: {
    background: Colors.background.primary,
    surface: Colors.background.secondary,
    card: Colors.background.card,
    text: Colors.text.primary,
    textSecondary: Colors.text.secondary,
    border: Colors.neutral[700],
    primary: Colors.primary[400],
    accent: Colors.accent[400],
  },
  
  // 为未来的浅色主题预留
  light: {
    background: Colors.neutral[50],
    surface: Colors.neutral[100],
    card: '#FFFFFF',
    text: Colors.neutral[900],
    textSecondary: Colors.neutral[600],
    border: Colors.neutral[200],
    primary: Colors.primary[500],
    accent: Colors.accent[500],
  },
};

export default Colors;
