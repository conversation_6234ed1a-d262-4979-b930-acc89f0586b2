import { StyleSheet, Dimensions } from 'react-native';
import Colors from './colors';
import { TextStyles } from './typography';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// iOS标准间距
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// iOS标准圆角
export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// 阴影样式 - iOS风格
export const Shadows = {
  // 小阴影
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  
  // 中等阴影
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  
  // 大阴影
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
  
  // 超大阴影
  xl: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.37,
    shadowRadius: 7.49,
    elevation: 12,
  },

  // 神秘发光效果
  mystical: {
    shadowColor: Colors.primary[400],
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 8,
  },

  // 金色发光
  golden: {
    shadowColor: Colors.accent[400],
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.6,
    shadowRadius: 8,
    elevation: 6,
  },
};

// 通用样式
export const CommonStyles = StyleSheet.create({
  // 容器样式
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  safeArea: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  // 内容区域
  content: {
    flex: 1,
    paddingHorizontal: Spacing.md,
  },

  contentWithPadding: {
    flex: 1,
    padding: Spacing.md,
  },

  // 居中布局
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },

  // 卡片样式
  card: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    ...Shadows.md,
  },

  mysticalCard: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.primary[600],
    ...Shadows.mystical,
  },

  // 按钮样式
  button: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.sm,
  },

  primaryButton: {
    backgroundColor: Colors.primary[500],
  },

  secondaryButton: {
    backgroundColor: Colors.background.card,
    borderWidth: 1,
    borderColor: Colors.primary[400],
  },

  accentButton: {
    backgroundColor: Colors.accent[500],
  },

  // 文本样式
  title: {
    ...TextStyles.title1,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },

  subtitle: {
    ...TextStyles.title3,
    color: Colors.text.secondary,
    marginBottom: Spacing.md,
  },

  body: {
    ...TextStyles.body,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },

  caption: {
    ...TextStyles.caption1,
    color: Colors.text.tertiary,
  },

  // 输入框样式
  input: {
    backgroundColor: Colors.background.card,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.neutral[600],
    color: Colors.text.primary,
    ...TextStyles.body,
  },

  inputFocused: {
    borderColor: Colors.primary[400],
    ...Shadows.mystical,
  },

  // 分隔线
  separator: {
    height: 1,
    backgroundColor: Colors.neutral[700],
    marginVertical: Spacing.md,
  },

  // 网格布局
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  gridItem: {
    width: (screenWidth - Spacing.md * 3) / 2,
    marginBottom: Spacing.md,
  },

  // 列表项
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral[700],
  },

  // 头部样式
  header: {
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.background.secondary,
  },

  // 底部安全区域
  bottomSafeArea: {
    backgroundColor: Colors.background.secondary,
  },

  // 加载状态
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background.primary,
  },

  // 错误状态
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.background.primary,
  },

  errorText: {
    ...TextStyles.body,
    color: Colors.status.error,
    textAlign: 'center',
    marginBottom: Spacing.md,
  },

  // 空状态
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },

  emptyText: {
    ...TextStyles.body,
    color: Colors.text.tertiary,
    textAlign: 'center',
  },

  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.background.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },

  modalContent: {
    backgroundColor: Colors.background.secondary,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    margin: Spacing.lg,
    maxHeight: screenHeight * 0.8,
    ...Shadows.xl,
  },

  // 标签样式
  tag: {
    backgroundColor: Colors.primary[600],
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignSelf: 'flex-start',
  },

  tagText: {
    ...TextStyles.caption2,
    color: Colors.text.primary,
    fontWeight: '600',
  },
});

// 屏幕尺寸
export const Layout = {
  window: {
    width: screenWidth,
    height: screenHeight,
  },
  isSmallDevice: screenWidth < 375,
  isLargeDevice: screenWidth >= 414,
};

export default CommonStyles;
