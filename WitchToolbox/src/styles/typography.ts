import { Platform } from 'react-native';

// iOS现代风格字体配置
export const Typography = {
  // 字体家族
  fontFamily: {
    // iOS系统字体
    system: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'System',
    }),
    
    // iOS San Francisco字体变体
    systemBold: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'System',
    }),
    
    // 神秘主题装饰字体（可选）
    mystical: Platform.select({
      ios: 'Avenir Next',
      android: 'sans-serif-light',
      default: 'System',
    }),
  },

  // 字体大小 - 遵循iOS Human Interface Guidelines
  fontSize: {
    // 标题
    largeTitle: 34,    // iOS Large Title
    title1: 28,        // iOS Title 1
    title2: 22,        // iOS Title 2
    title3: 20,        // iOS Title 3
    
    // 标题
    headline: 17,      // iOS Headline
    body: 17,          // iOS Body
    callout: 16,       // iOS Callout
    subhead: 15,       // iOS Subhead
    footnote: 13,      // iOS Footnote
    caption1: 12,      // iOS Caption 1
    caption2: 11,      // iOS Caption 2
    
    // 自定义大小
    xs: 10,
    sm: 12,
    base: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
    '3xl': 24,
    '4xl': 28,
    '5xl': 32,
    '6xl': 36,
  },

  // 字体粗细
  fontWeight: {
    thin: '100',
    ultraLight: '200',
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    heavy: '800',
    black: '900',
  },

  // 行高
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },

  // 字母间距
  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
    widest: 1,
  },
};

// 预定义文本样式 - iOS风格
export const TextStyles = {
  // 大标题
  largeTitle: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.largeTitle,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize.largeTitle * Typography.lineHeight.tight,
    letterSpacing: Typography.letterSpacing.tight,
  },

  // 标题1
  title1: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.title1,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize.title1 * Typography.lineHeight.tight,
    letterSpacing: Typography.letterSpacing.tight,
  },

  // 标题2
  title2: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.title2,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: Typography.fontSize.title2 * Typography.lineHeight.snug,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 标题3
  title3: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.title3,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.title3 * Typography.lineHeight.snug,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 标题
  headline: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.headline,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.headline * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 正文
  body: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.body,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.body * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 正文粗体
  bodyBold: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.body,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.body * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 标注
  callout: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.callout,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.callout * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 副标题
  subhead: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.subhead,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.subhead * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 脚注
  footnote: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.footnote,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.footnote * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 说明文字1
  caption1: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.caption1,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.caption1 * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 说明文字2
  caption2: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.caption2,
    fontWeight: Typography.fontWeight.regular,
    lineHeight: Typography.fontSize.caption2 * Typography.lineHeight.normal,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 神秘主题特殊样式
  mysticalTitle: {
    fontFamily: Typography.fontFamily.mystical,
    fontSize: Typography.fontSize.title2,
    fontWeight: Typography.fontWeight.light,
    lineHeight: Typography.fontSize.title2 * Typography.lineHeight.relaxed,
    letterSpacing: Typography.letterSpacing.wide,
  },

  // 按钮文字
  button: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.body,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.body * Typography.lineHeight.tight,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // 导航标题
  navigationTitle: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.headline,
    fontWeight: Typography.fontWeight.semibold,
    lineHeight: Typography.fontSize.headline * Typography.lineHeight.tight,
    letterSpacing: Typography.letterSpacing.normal,
  },

  // Tab标签
  tabLabel: {
    fontFamily: Typography.fontFamily.system,
    fontSize: Typography.fontSize.caption1,
    fontWeight: Typography.fontWeight.medium,
    lineHeight: Typography.fontSize.caption1 * Typography.lineHeight.tight,
    letterSpacing: Typography.letterSpacing.normal,
  },
};

export default Typography;
