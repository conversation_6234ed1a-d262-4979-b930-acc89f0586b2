import * as Haptics from 'expo-haptics';

// 触觉反馈工具类
export const hapticFeedback = {
  // 轻微触觉反馈 - 用于按钮点击
  light: async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      // 静默处理错误，某些设备可能不支持触觉反馈
    }
  },

  // 中等触觉反馈 - 用于重要操作
  medium: async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      // 静默处理错误
    }
  },

  // 强烈触觉反馈 - 用于关键操作
  heavy: async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } catch (error) {
      // 静默处理错误
    }
  },

  // 成功反馈
  success: async () => {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      // 静默处理错误
    }
  },

  // 警告反馈
  warning: async () => {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } catch (error) {
      // 静默处理错误
    }
  },

  // 错误反馈
  error: async () => {
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } catch (error) {
      // 静默处理错误
    }
  },

  // 选择反馈 - 用于滑动选择
  selection: async () => {
    try {
      await Haptics.selectionAsync();
    } catch (error) {
      // 静默处理错误
    }
  },
};

export default hapticFeedback;
