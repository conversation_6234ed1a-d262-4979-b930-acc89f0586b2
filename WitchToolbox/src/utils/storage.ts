import AsyncStorage from '@react-native-async-storage/async-storage';

// 存储键名常量
export const STORAGE_KEYS = {
  DIVINATION_HISTORY: 'divination_history',
  USER_PREFERENCES: 'user_preferences',
  LEARNING_PROGRESS: 'learning_progress',
  PRACTICE_RECORDS: 'practice_records',
  DAILY_INSPIRATION_DATE: 'daily_inspiration_date',
  FAVORITE_CRYSTALS: 'favorite_crystals',
  MEDITATION_SESSIONS: 'meditation_sessions',
};

// 通用存储操作
export const storage = {
  // 存储数据
  async setItem(key: string, value: any): Promise<boolean> {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
      return true;
    } catch (error) {
      console.error('Error storing data:', error);
      return false;
    }
  },

  // 获取数据
  async getItem(key: string, defaultValue: any = null): Promise<any> {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : defaultValue;
    } catch (error) {
      console.error('Error retrieving data:', error);
      return defaultValue;
    }
  },

  // 删除数据
  async removeItem(key: string): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error removing data:', error);
      return false;
    }
  },

  // 清空所有数据
  async clear(): Promise<boolean> {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing storage:', error);
      return false;
    }
  },

  // 获取所有键名
  async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  },
};

// 占卜历史记录
export const divinationHistory = {
  // 保存占卜记录
  async save(record) {
    try {
      const history = await storage.getItem(STORAGE_KEYS.DIVINATION_HISTORY, []);
      const newRecord = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        ...record,
      };
      
      // 保持最近50条记录
      const updatedHistory = [newRecord, ...history].slice(0, 50);
      await storage.setItem(STORAGE_KEYS.DIVINATION_HISTORY, updatedHistory);
      return newRecord;
    } catch (error) {
      console.error('Error saving divination record:', error);
      return null;
    }
  },

  // 获取历史记录
  async getAll() {
    return await storage.getItem(STORAGE_KEYS.DIVINATION_HISTORY, []);
  },

  // 删除记录
  async delete(recordId) {
    try {
      const history = await storage.getItem(STORAGE_KEYS.DIVINATION_HISTORY, []);
      const updatedHistory = history.filter(record => record.id !== recordId);
      await storage.setItem(STORAGE_KEYS.DIVINATION_HISTORY, updatedHistory);
      return true;
    } catch (error) {
      console.error('Error deleting divination record:', error);
      return false;
    }
  },

  // 清空历史记录
  async clear() {
    return await storage.removeItem(STORAGE_KEYS.DIVINATION_HISTORY);
  },
};

// 学习进度
export const learningProgress = {
  // 保存课程进度
  async saveCourseProgress(courseId, progress) {
    try {
      const allProgress = await storage.getItem(STORAGE_KEYS.LEARNING_PROGRESS, {});
      allProgress[courseId] = {
        ...allProgress[courseId],
        ...progress,
        lastUpdated: new Date().toISOString(),
      };
      await storage.setItem(STORAGE_KEYS.LEARNING_PROGRESS, allProgress);
      return true;
    } catch (error) {
      console.error('Error saving learning progress:', error);
      return false;
    }
  },

  // 获取课程进度
  async getCourseProgress(courseId) {
    const allProgress = await storage.getItem(STORAGE_KEYS.LEARNING_PROGRESS, {});
    return allProgress[courseId] || { completed: false, progress: 0 };
  },

  // 获取所有进度
  async getAllProgress() {
    return await storage.getItem(STORAGE_KEYS.LEARNING_PROGRESS, {});
  },

  // 标记课程完成
  async markCourseComplete(courseId) {
    return await this.saveCourseProgress(courseId, { 
      completed: true, 
      progress: 100,
      completedAt: new Date().toISOString(),
    });
  },
};

// 实践记录
export const practiceRecords = {
  // 保存实践记录
  async save(record) {
    try {
      const records = await storage.getItem(STORAGE_KEYS.PRACTICE_RECORDS, []);
      const newRecord = {
        id: Date.now().toString(),
        date: new Date().toISOString().split('T')[0], // YYYY-MM-DD
        timestamp: new Date().toISOString(),
        ...record,
      };
      
      const updatedRecords = [newRecord, ...records];
      await storage.setItem(STORAGE_KEYS.PRACTICE_RECORDS, updatedRecords);
      return newRecord;
    } catch (error) {
      console.error('Error saving practice record:', error);
      return null;
    }
  },

  // 获取今日记录
  async getTodayRecords() {
    const today = new Date().toISOString().split('T')[0];
    const allRecords = await storage.getItem(STORAGE_KEYS.PRACTICE_RECORDS, []);
    return allRecords.filter(record => record.date === today);
  },

  // 获取指定日期范围的记录
  async getRecordsByDateRange(startDate, endDate) {
    const allRecords = await storage.getItem(STORAGE_KEYS.PRACTICE_RECORDS, []);
    return allRecords.filter(record => 
      record.date >= startDate && record.date <= endDate
    );
  },

  // 获取所有记录
  async getAll() {
    return await storage.getItem(STORAGE_KEYS.PRACTICE_RECORDS, []);
  },
};

// 用户偏好设置
export const userPreferences = {
  // 保存偏好设置
  async save(preferences) {
    const current = await storage.getItem(STORAGE_KEYS.USER_PREFERENCES, {});
    const updated = { ...current, ...preferences };
    return await storage.setItem(STORAGE_KEYS.USER_PREFERENCES, updated);
  },

  // 获取偏好设置
  async get() {
    return await storage.getItem(STORAGE_KEYS.USER_PREFERENCES, {
      theme: 'dark',
      language: 'zh',
      notifications: true,
      soundEnabled: true,
    });
  },

  // 获取特定偏好
  async getPreference(key, defaultValue) {
    const preferences = await this.get();
    return preferences[key] !== undefined ? preferences[key] : defaultValue;
  },
};

// 收藏的水晶
export const favoriteCrystals = {
  // 添加收藏
  async add(crystalId) {
    try {
      const favorites = await storage.getItem(STORAGE_KEYS.FAVORITE_CRYSTALS, []);
      if (!favorites.includes(crystalId)) {
        favorites.push(crystalId);
        await storage.setItem(STORAGE_KEYS.FAVORITE_CRYSTALS, favorites);
      }
      return true;
    } catch (error) {
      console.error('Error adding favorite crystal:', error);
      return false;
    }
  },

  // 移除收藏
  async remove(crystalId) {
    try {
      const favorites = await storage.getItem(STORAGE_KEYS.FAVORITE_CRYSTALS, []);
      const updated = favorites.filter(id => id !== crystalId);
      await storage.setItem(STORAGE_KEYS.FAVORITE_CRYSTALS, updated);
      return true;
    } catch (error) {
      console.error('Error removing favorite crystal:', error);
      return false;
    }
  },

  // 获取所有收藏
  async getAll() {
    return await storage.getItem(STORAGE_KEYS.FAVORITE_CRYSTALS, []);
  },

  // 检查是否已收藏
  async isFavorite(crystalId) {
    const favorites = await this.getAll();
    return favorites.includes(crystalId);
  },
};

export default storage;
