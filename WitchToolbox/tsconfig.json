{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "noImplicitAny": false, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/styles/*": ["src/styles/*"], "@/utils/*": ["src/utils/*"], "@/data/*": ["src/data/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"]}